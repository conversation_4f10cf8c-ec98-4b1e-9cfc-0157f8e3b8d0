/**
 * Professional Service - Manages professional profiles and directory
 */

class ProfessionalService {
  constructor() {
    this.storageKey = 'zentro_professionals';
    this.appsStorageKey = 'zentro_apps';
  }

  // Get all professionals
  getAllProfessionals() {
    try {
      const stored = localStorage.getItem(this.storageKey);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Error loading professionals:', error);
      return [];
    }
  }

  // Get professional by user ID
  getProfessionalByUserId(userId) {
    const professionals = this.getAllProfessionals();
    return professionals.find(prof => prof.userId === userId);
  }

  // Create or update professional profile
  saveProfessional(professionalData) {
    try {
      const professionals = this.getAllProfessionals();
      const existingIndex = professionals.findIndex(prof => prof.userId === professionalData.userId);

      const professional = {
        id: professionalData.id || Date.now().toString(),
        ...professionalData,
        updatedAt: new Date().toISOString(),
        createdAt: professionalData.createdAt || new Date().toISOString(),
      };

      if (existingIndex >= 0) {
        professionals[existingIndex] = professional;
      } else {
        professionals.push(professional);
      }

      localStorage.setItem(this.storageKey, JSON.stringify(professionals));
      return professional;
    } catch (error) {
      console.error('Error saving professional:', error);
      throw error;
    }
  }

  // Delete professional profile
  deleteProfessional(userId) {
    try {
      const professionals = this.getAllProfessionals();
      const filtered = professionals.filter(prof => prof.userId !== userId);
      localStorage.setItem(this.storageKey, JSON.stringify(filtered));
      return true;
    } catch (error) {
      console.error('Error deleting professional:', error);
      return false;
    }
  }

  // Search professionals
  searchProfessionals(query, filters = {}) {
    const professionals = this.getAllProfessionals();
    let filtered = [...professionals];

    // Text search
    if (query) {
      const searchTerm = query.toLowerCase();
      filtered = filtered.filter(prof =>
        prof.name?.toLowerCase().includes(searchTerm) ||
        prof.title?.toLowerCase().includes(searchTerm) ||
        prof.bio?.toLowerCase().includes(searchTerm) ||
        prof.skills?.some(skill => skill.toLowerCase().includes(searchTerm)) ||
        prof.location?.toLowerCase().includes(searchTerm)
      );
    }

    // Visibility filter
    if (filters.visibility && filters.visibility !== 'all') {
      filtered = filtered.filter(prof => prof.visibility === filters.visibility);
    }

    // Skills filter
    if (filters.skills && filters.skills.length > 0) {
      filtered = filtered.filter(prof =>
        prof.skills?.some(skill =>
          filters.skills.some(filterSkill =>
            skill.toLowerCase().includes(filterSkill.toLowerCase())
          )
        )
      );
    }

    // Experience filter
    if (filters.minExperience) {
      filtered = filtered.filter(prof =>
        prof.experience && prof.experience >= filters.minExperience
      );
    }

    // Location filter
    if (filters.location) {
      filtered = filtered.filter(prof =>
        prof.location?.toLowerCase().includes(filters.location.toLowerCase())
      );
    }

    return filtered;
  }

  // Get professionals by visibility
  getProfessionalsByVisibility(visibility, currentUserId = null) {
    const professionals = this.getAllProfessionals();

    switch (visibility) {
      case 'public':
        return professionals.filter(prof => prof.visibility === 'public');

      case 'friends':
        if (!currentUserId) return [];
        const friends = this.getFriends(currentUserId);
        return professionals.filter(prof =>
          prof.visibility === 'friends' &&
          friends.some(friend => friend.id === prof.userId)
        );

      case 'recruiters':
        return professionals.filter(prof => prof.visibility === 'recruiters');

      default:
        return professionals;
    }
  }

  // Get user's friends (helper method)
  getFriends(userId) {
    try {
      const friends = localStorage.getItem('zentro_friends');
      return friends ? JSON.parse(friends) : [];
    } catch (error) {
      console.error('Error loading friends:', error);
      return [];
    }
  }

  // App management methods
  getAllApps() {
    try {
      const stored = localStorage.getItem(this.appsStorageKey);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Error loading apps:', error);
      return [];
    }
  }

  // Get apps by user ID
  getAppsByUserId(userId) {
    const apps = this.getAllApps();
    return apps.filter(app => app.developerId === userId);
  }

  // Save app
  saveApp(appData) {
    try {
      const apps = this.getAllApps();
      const existingIndex = apps.findIndex(app => app.id === appData.id);

      const app = {
        id: appData.id || Date.now().toString(),
        ...appData,
        updatedAt: new Date().toISOString(),
        createdAt: appData.createdAt || new Date().toISOString(),
      };

      if (existingIndex >= 0) {
        apps[existingIndex] = app;
      } else {
        apps.push(app);
      }

      localStorage.setItem(this.appsStorageKey, JSON.stringify(apps));
      return app;
    } catch (error) {
      console.error('Error saving app:', error);
      throw error;
    }
  }

  // Delete app
  deleteApp(appId) {
    try {
      const apps = this.getAllApps();
      const filtered = apps.filter(app => app.id !== appId);
      localStorage.setItem(this.appsStorageKey, JSON.stringify(filtered));
      return true;
    } catch (error) {
      console.error('Error deleting app:', error);
      return false;
    }
  }

  // Get public apps only
  getPublicApps() {
    const apps = this.getAllApps();
    return apps.filter(app => app.visibility === 'public');
  }

  // Search apps
  searchApps(query, filters = {}) {
    const apps = this.getPublicApps();
    let filtered = [...apps];

    // Text search
    if (query) {
      const searchTerm = query.toLowerCase();
      filtered = filtered.filter(app =>
        app.title?.toLowerCase().includes(searchTerm) ||
        app.description?.toLowerCase().includes(searchTerm) ||
        app.category?.toLowerCase().includes(searchTerm) ||
        app.tags?.some(tag => tag.toLowerCase().includes(searchTerm))
      );
    }

    // Category filter
    if (filters.category && filters.category !== 'all') {
      filtered = filtered.filter(app => app.category === filters.category);
    }

    // Sort by rating, downloads, or date
    if (filters.sortBy) {
      filtered.sort((a, b) => {
        switch (filters.sortBy) {
          case 'rating':
            return (b.rating || 0) - (a.rating || 0);
          case 'downloads':
            return (b.downloads || 0) - (a.downloads || 0);
          case 'newest':
            return new Date(b.createdAt) - new Date(a.createdAt);
          case 'oldest':
            return new Date(a.createdAt) - new Date(b.createdAt);
          default:
            return 0;
        }
      });
    }

    return filtered;
  }

  // Get app categories
  getAppCategories() {
    const apps = this.getAllApps();
    const categories = [...new Set(apps.map(app => app.category).filter(Boolean))];
    return categories.sort();
  }

  // Get popular skills
  getPopularSkills() {
    const professionals = this.getAllProfessionals();
    const skillCounts = {};

    professionals.forEach(prof => {
      if (prof.skills) {
        prof.skills.forEach(skill => {
          skillCounts[skill] = (skillCounts[skill] || 0) + 1;
        });
      }
    });

    return Object.entries(skillCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 20)
      .map(([skill]) => skill);
  }

  // Get statistics
  getStatistics() {
    const professionals = this.getAllProfessionals();
    const apps = this.getAllApps();

    return {
      totalProfessionals: professionals.length,
      publicProfessionals: professionals.filter(p => p.visibility === 'public').length,
      totalApps: apps.length,
      publicApps: apps.filter(a => a.visibility === 'public').length,
      categories: this.getAppCategories().length,
      popularSkills: this.getPopularSkills().slice(0, 5),
    };
  }

  // Clear all app showcase data
  clearAppShowcase() {
    try {
      localStorage.removeItem(this.appsStorageKey);
      console.log('✅ App showcase database cleared');
      return true;
    } catch (error) {
      console.error('Error clearing app showcase:', error);
      return false;
    }
  }

  // Initialize with empty showcase (call this to clear database)
  initializeEmptyShowcase() {
    try {
      localStorage.setItem(this.appsStorageKey, JSON.stringify([]));
      console.log('✅ App showcase database initialized as empty');
      return true;
    } catch (error) {
      console.error('Error initializing empty showcase:', error);
      return false;
    }
  }
}

// Create and export singleton instance
const professionalService = new ProfessionalService();
export default professionalService;
