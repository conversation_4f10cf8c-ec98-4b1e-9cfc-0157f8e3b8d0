# Zentro Chat

A modern social platform with chat, blog, music, and profile features.

## Features

### 1. Profile Customization
- Gmail ID integration
- Customizable profile pictures and banners
- Personal information management

### 2. Social Features
- Posts with comments and likes
- Real-time updates for comments and interactions
- Public/private content visibility options

### 3. Music Integration
- In-app music player with comprehensive search
- Genre filtering and music discovery
- Playlist management and favorites

### 4. Blog System
- Create and publish blog posts
- Toggle between public and private visibility
- Advanced research assistant for content creation
- AI-powered writing assistance

## Getting Started

This project was bootstrapped with [Create React App](https://github.com/facebook/create-react-app).

In the project directory, you can run:

### `npm start`

Runs the app in the development mode.\
Open [http://localhost:3000](http://localhost:3000) to view it in your browser.

## App Icons

To generate app icons for the Music, Blog, and Profile features:

1. Start the development server
2. Navigate to http://localhost:3000/apps/create-icons.html
3. Click on each icon to download it
4. Place the downloaded icons in the `public/apps` directory

## Research Assistant

The blog editor includes a powerful research assistant that can help you:

1. Generate blog outlines based on your topic
2. Choose from different blog formats (detailed article, listicle, how-to guide, etc.)
3. Get content suggestions and references
4. Insert the generated content directly into your blog post

## Authentication

The app uses Firebase authentication with a custom UI:

- Email/password login
- Google authentication
- Session management to prevent credential issues

## Comments and Interactions

Comments and replies now appear immediately without requiring page refreshes:

- Real-time updates using state management
- Local storage persistence
- Success messages for user feedback

## Learn More

You can learn more in the [Create React App documentation](https://facebook.github.io/create-react-app/docs/getting-started).

To learn React, check out the [React documentation](https://reactjs.org/).
