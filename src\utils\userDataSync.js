// Utility to sync user data between different localStorage keys
// This ensures compatibility between different parts of the app

class UserDataSync {
  constructor() {
    this.KEYS = {
      ALL_USERS: 'zentro_all_users',
      REGISTERED_USERS: 'zentro_registered_users'
    };
  }

  // Get all users from any available source
  getAllUsers() {
    let users = [];
    
    // Try zentro_all_users first
    try {
      const allUsers = JSON.parse(localStorage.getItem(this.KEYS.ALL_USERS) || '[]');
      if (allUsers.length > 0) {
        users = allUsers;
      }
    } catch (error) {
      console.error('Error reading zentro_all_users:', error);
    }

    // If no users found, try zentro_registered_users
    if (users.length === 0) {
      try {
        const registeredUsers = JSON.parse(localStorage.getItem(this.KEYS.REGISTERED_USERS) || '[]');
        users = registeredUsers;
      } catch (error) {
        console.error('Error reading zentro_registered_users:', error);
      }
    }

    return users;
  }

  // Add or update a user in both storage locations
  syncUser(userProfile) {
    if (!userProfile || !userProfile.uid) {
      console.warn('Invalid user profile provided to syncUser');
      return;
    }

    try {
      // Update zentro_all_users
      const allUsers = JSON.parse(localStorage.getItem(this.KEYS.ALL_USERS) || '[]');
      const existingIndex = allUsers.findIndex(u => u.uid === userProfile.uid);
      
      if (existingIndex >= 0) {
        allUsers[existingIndex] = { ...allUsers[existingIndex], ...userProfile };
      } else {
        allUsers.push(userProfile);
      }
      
      localStorage.setItem(this.KEYS.ALL_USERS, JSON.stringify(allUsers));

      // Update zentro_registered_users
      const registeredUsers = JSON.parse(localStorage.getItem(this.KEYS.REGISTERED_USERS) || '[]');
      const existingRegIndex = registeredUsers.findIndex(u => u.uid === userProfile.uid);
      
      if (existingRegIndex >= 0) {
        registeredUsers[existingRegIndex] = { ...registeredUsers[existingRegIndex], ...userProfile };
      } else {
        registeredUsers.push(userProfile);
      }
      
      localStorage.setItem(this.KEYS.REGISTERED_USERS, JSON.stringify(registeredUsers));

      console.log('✅ User synced to both storage locations:', userProfile.displayName || userProfile.email);
    } catch (error) {
      console.error('Error syncing user data:', error);
    }
  }

  // Find a user by ID
  findUserById(userId) {
    const users = this.getAllUsers();
    return users.find(user => user.uid === userId);
  }

  // Get user display name
  getUserDisplayName(userId) {
    if (!userId) return 'Unknown User';
    
    const user = this.findUserById(userId);
    if (user) {
      return user.displayName || user.username || user.email || 'Unknown User';
    }
    
    return 'Unknown User';
  }

  // Sync all users from one storage to another (migration helper)
  migrateUsers() {
    try {
      const allUsers = JSON.parse(localStorage.getItem(this.KEYS.ALL_USERS) || '[]');
      const registeredUsers = JSON.parse(localStorage.getItem(this.KEYS.REGISTERED_USERS) || '[]');

      // Merge both arrays, removing duplicates
      const mergedUsers = [...allUsers];
      
      registeredUsers.forEach(regUser => {
        const exists = mergedUsers.find(u => u.uid === regUser.uid);
        if (!exists) {
          mergedUsers.push(regUser);
        }
      });

      // Save merged data to both locations
      localStorage.setItem(this.KEYS.ALL_USERS, JSON.stringify(mergedUsers));
      localStorage.setItem(this.KEYS.REGISTERED_USERS, JSON.stringify(mergedUsers));

      console.log('✅ User data migration completed. Total users:', mergedUsers.length);
      return mergedUsers;
    } catch (error) {
      console.error('Error during user migration:', error);
      return [];
    }
  }

  // Debug function to show current state
  debugUserData() {
    console.log('🔍 User Data Debug:');
    console.log('zentro_all_users:', JSON.parse(localStorage.getItem(this.KEYS.ALL_USERS) || '[]'));
    console.log('zentro_registered_users:', JSON.parse(localStorage.getItem(this.KEYS.REGISTERED_USERS) || '[]'));
  }
}

// Create singleton instance
const userDataSync = new UserDataSync();
export default userDataSync;
