{"name": "zentro-chat", "version": "0.1.0", "private": true, "dependencies": {"@craco/craco": "^7.0.0", "@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@giphy/js-fetch-api": "^5.6.0", "@giphy/react-components": "^10.0.1", "axios": "^1.8.4", "classnames": "^2.5.1", "emoji-mart": "^5.6.0", "emoji-picker-react": "^4.12.2", "firebase": "^11.6.0", "framer-motion": "^12.12.2", "openai": "^4.103.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-markdown": "^8.0.7", "react-router-dom": "^7.5.0", "react-scripts": "5.0.1", "remark-gfm": "^3.0.1", "web-vitals": "^4.2.4"}, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@tailwindcss/postcss": "^4.1.3", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "postcss-loader": "^8.1.1", "tailwindcss": "^4.1.3"}}