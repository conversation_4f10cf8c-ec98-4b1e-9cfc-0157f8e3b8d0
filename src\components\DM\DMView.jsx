import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import RealTimeDMSidebar from './RealTimeDMSidebar';
import RealTimeDMChatRoom from './RealTimeDMChatRoom';
import ZentroBotChat from './ZentroBotChat';
import DataManager from '../Debug/DataManager';
import { getTheme, applyTheme, defaultTheme } from '../../styles/themes';
import { useUser } from '../../contexts/UserContext';

const DMView = () => {
  const location = useLocation();
  const { canAccessDataManager } = useUser();
  const [selectedChat, setSelectedChat] = useState(null);
  const [currentTheme, setCurrentTheme] = useState(() => {
    const savedTheme = localStorage.getItem('dm_theme');
    return savedTheme ? getTheme(savedTheme) : defaultTheme;
  });
  const [showDataManager, setShowDataManager] = useState(false);

  // Check for selectedChat from navigation state
  useEffect(() => {
    if (location.state?.selectedChat) {
      console.log('DMView: Setting selected chat from navigation:', location.state.selectedChat);
      setSelectedChat(location.state.selectedChat);
    }
  }, [location.state]);

  // Apply theme on mount
  useEffect(() => {
    applyTheme(currentTheme);
  }, [currentTheme]);

  const handleSelectChat = (chat) => {
    setSelectedChat(chat);
  };

  const handleBackToSidebar = () => {
    setSelectedChat(null);
  };

  return (
    <div
      className="h-screen flex"
      style={{ backgroundColor: currentTheme.colors.background }}
    >
      {/* DM Sidebar - Minimal when chat is selected */}
      <div
        className={`${selectedChat ? 'w-16' : 'w-80'} border-r transition-all duration-300`}
        style={{ borderColor: currentTheme.colors.borderMuted }}
      >
        <RealTimeDMSidebar
          onSelectChat={handleSelectChat}
          selectedChat={selectedChat}
          currentTheme={currentTheme}
          isMinimal={selectedChat !== null}
        />
      </div>

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Chat Area */}
        <div className="w-full">
          {selectedChat ? (
            selectedChat?.otherUser?.id === 'zentro_bot' || selectedChat?.otherUser?.isBot || selectedChat.id === 'zentro_bot_chat' || selectedChat.name === 'Zenny' || selectedChat?.otherUser?.name === 'Zenny' ? (
              <ZentroBotChat
                theme={currentTheme}
                onBack={handleBackToSidebar}
              />
            ) : (
              <RealTimeDMChatRoom
                chatUser={selectedChat.otherUser || selectedChat}
                chatId={selectedChat.id}
                onBack={handleBackToSidebar}
                theme={currentTheme}
              />
            )
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div
                  className="text-6xl mx-auto mb-4"
                  style={{ color: currentTheme.colors.textMuted }}
                >
                  💬
                </div>
                <h3
                  className="text-2xl font-semibold mb-2"
                  style={{ color: currentTheme.colors.text }}
                >
                  Welcome to Zentro Messages
                </h3>
                <p
                  className="mb-6 max-w-md"
                  style={{ color: currentTheme.colors.textMuted }}
                >
                  Select a conversation from the sidebar to start chatting with friends in real-time,
                  share media, and enjoy our beautiful themes.
                </p>
                <div
                  className="space-y-2 text-sm"
                  style={{ color: currentTheme.colors.textMuted }}
                >
                  <p>• Share photos and videos</p>
                  <p>• Multiple beautiful themes</p>
                  <p>• Real-time messaging</p>
                  <p>• Reactions and replies</p>
                </div>

                {/* Debug Data Manager Button - Admin Only */}
                {canAccessDataManager() && (
                  <div className="mt-6">
                    <button
                      onClick={() => setShowDataManager(true)}
                      className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors text-sm"
                    >
                      🗑️ Data Manager (Admin)
                    </button>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Data Manager Modal */}
      {showDataManager && (
        <DataManager onClose={() => setShowDataManager(false)} />
      )}
    </div>
  );
};

export default DMView;
