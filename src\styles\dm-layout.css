/* DM Layout Fixes - Ensures proper full-screen utilization */

/* Root container fixes */
html, body, #root {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

/* Ensure DM containers fill the screen properly */
.dm-container {
  height: 100vh;
  height: 100dvh; /* Dynamic viewport height for mobile */
  width: 100vw;
  max-height: 100vh;
  max-height: 100dvh;
  overflow: hidden;
  position: relative;
}

/* Mobile-first responsive design */
@media (max-width: 1023px) {
  .dm-sidebar {
    width: 100vw !important;
    max-width: 100vw;
  }
  
  .dm-chat-area {
    width: 100vw !important;
    max-width: 100vw;
  }
  
  /* Hide sidebar when chat is selected on mobile */
  .dm-sidebar.hidden-mobile {
    display: none !important;
  }
  
  /* Full width chat on mobile */
  .dm-chat-area.full-mobile {
    width: 100vw !important;
    flex: 1 !important;
  }
}

/* Desktop layout */
@media (min-width: 1024px) {
  .dm-sidebar {
    width: 320px;
    min-width: 320px;
    max-width: 320px;
  }
  
  .dm-chat-area {
    flex: 1;
    min-width: 0;
  }
}

/* Message area fixes */
.dm-messages-area {
  flex: 1;
  overflow-y: auto;
  height: 0; /* Important for flex child to scroll properly */
  min-height: 0;
}

/* Input area fixes */
.dm-input-area {
  flex-shrink: 0;
  width: 100%;
}

/* Header fixes */
.dm-header {
  flex-shrink: 0;
  width: 100%;
}

/* Prevent horizontal scroll */
.dm-no-scroll {
  overflow-x: hidden;
  max-width: 100vw;
}

/* Mobile safe area adjustments */
@supports (padding: max(0px)) {
  .dm-safe-top {
    padding-top: max(16px, env(safe-area-inset-top));
  }
  
  .dm-safe-bottom {
    padding-bottom: max(16px, env(safe-area-inset-bottom));
  }
  
  .dm-safe-left {
    padding-left: max(16px, env(safe-area-inset-left));
  }
  
  .dm-safe-right {
    padding-right: max(16px, env(safe-area-inset-right));
  }
}

/* Fix for iOS viewport issues */
@supports (-webkit-touch-callout: none) {
  .dm-container {
    height: -webkit-fill-available;
  }
}

/* Ensure proper stacking */
.dm-layer-base {
  z-index: 1;
}

.dm-layer-content {
  z-index: 10;
}

.dm-layer-modal {
  z-index: 50;
}

/* Responsive text sizing */
@media (max-width: 640px) {
  .dm-text-responsive {
    font-size: 14px;
  }
  
  .dm-text-responsive-lg {
    font-size: 16px;
  }
}

@media (min-width: 641px) {
  .dm-text-responsive {
    font-size: 16px;
  }
  
  .dm-text-responsive-lg {
    font-size: 18px;
  }
}

/* Button sizing for touch devices */
@media (hover: none) and (pointer: coarse) {
  .dm-touch-target {
    min-height: 44px;
    min-width: 44px;
    padding: 12px;
  }
}

/* Landscape mobile adjustments */
@media (max-width: 1023px) and (orientation: landscape) {
  .dm-container {
    height: 100vh;
  }
  
  .dm-header {
    padding: 8px 16px;
    min-height: 48px;
  }
  
  .dm-input-area {
    padding: 8px 16px;
  }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .dm-crisp-edges {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .dm-auto-dark {
    background-color: #0f0f23;
    color: #ffffff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .dm-animate {
    animation: none !important;
    transition: none !important;
  }
}

/* Focus management for accessibility */
.dm-focus-visible:focus-visible {
  outline: 2px solid #8b5cf6;
  outline-offset: 2px;
  border-radius: 4px;
}

/* Scrollbar styling */
.dm-custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.dm-custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.dm-custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(139, 92, 246, 0.3);
  border-radius: 3px;
}

.dm-custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(139, 92, 246, 0.5);
}

/* Utility classes */
.dm-full-height {
  height: 100vh;
  height: 100dvh;
}

.dm-full-width {
  width: 100vw;
}

.dm-no-overflow {
  overflow: hidden;
}

.dm-flex-1 {
  flex: 1;
  min-height: 0;
  min-width: 0;
}
