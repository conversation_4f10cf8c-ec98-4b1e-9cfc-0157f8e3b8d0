// Age-related utility functions for Zentro

/**
 * Calculate age from birth date
 * @param {string} birthDate - Birth date in YYYY-MM-DD format
 * @returns {number} Age in years
 */
export const calculateAge = (birthDate) => {
  if (!birthDate) return 0;
  
  const today = new Date();
  const birth = new Date(birthDate);
  let age = today.getFullYear() - birth.getFullYear();
  const monthDiff = today.getMonth() - birth.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--;
  }
  
  return age;
};

/**
 * Check if user is eligible for professional features
 * Professional features require users to be 21 or older
 * @param {string} birthDate - Birth date in YYYY-MM-DD format
 * @returns {boolean} True if eligible for professional features
 */
export const isProfessionalEligible = (birthDate) => {
  if (!birthDate) return false;
  return calculateAge(birthDate) >= 21;
};

/**
 * Get the date when user will become eligible for professional features
 * @param {string} birthDate - Birth date in YYYY-MM-DD format
 * @returns {Date|null} Date when user turns 21, or null if already eligible
 */
export const getProfessionalEligibilityDate = (birthDate) => {
  if (!birthDate) return null;
  
  const currentAge = calculateAge(birthDate);
  if (currentAge >= 21) return null;
  
  const birth = new Date(birthDate);
  const eligibilityDate = new Date(birth);
  eligibilityDate.setFullYear(birth.getFullYear() + 21);
  
  return eligibilityDate;
};

/**
 * Get user's current age from localStorage
 * @returns {number} Current age or 0 if not found
 */
export const getCurrentUserAge = () => {
  const birthDate = localStorage.getItem('zentro_user_birthDate');
  return birthDate ? calculateAge(birthDate) : 0;
};

/**
 * Check if current user is eligible for professional features
 * @returns {boolean} True if current user is eligible
 */
export const isCurrentUserProfessionalEligible = () => {
  const birthDate = localStorage.getItem('zentro_user_birthDate');
  return isProfessionalEligible(birthDate);
};

/**
 * Format age restriction message for professional features
 * @param {string} birthDate - Birth date in YYYY-MM-DD format
 * @returns {string} Formatted message explaining age restriction
 */
export const getProfessionalRestrictionMessage = (birthDate) => {
  if (!birthDate) return 'Professional features require age verification.';
  
  const currentAge = calculateAge(birthDate);
  if (currentAge >= 21) return '';
  
  const eligibilityDate = getProfessionalEligibilityDate(birthDate);
  const yearsLeft = 21 - currentAge;
  
  return `Professional features are available for users 21 and older. You'll be eligible in ${yearsLeft} year${yearsLeft !== 1 ? 's' : ''} (${eligibilityDate.toLocaleDateString()}).`;
};
