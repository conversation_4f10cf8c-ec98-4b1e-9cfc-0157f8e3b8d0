import React, { useState, useEffect } from 'react';
import RealTimeDMSidebar from './RealTimeDMSidebar';
import RealTimeDMChatRoom from './RealTimeDMChatRoom';
import ZentroBotChat from './ZentroBotChat';
import { useTheme } from '../../contexts/ThemeContext';
import { useNotifications } from '../Notifications/CustomNotificationSystem';
import notificationService from '../../services/notificationService';

const RealTimeDMView = () => {
  const [selectedChat, setSelectedChat] = useState(null);
  const { currentTheme, changeTheme, isLoading: themeLoading } = useTheme();

  // Initialize notification service
  const notificationHandler = useNotifications();

  // Initialize notification service
  useEffect(() => {
    notificationService.initialize(notificationHandler);
  }, [notificationHandler]);

  // Handle theme changes
  const handleThemeChange = (newTheme) => {
    changeTheme(newTheme);
  };

  const handleSelectChat = (chat) => {
    setSelectedChat(chat);
  };

  const handleBackToSidebar = () => {
    setSelectedChat(null);
  };

  // Check if this is Zentro Bot chat
  const isZentroBotChat = selectedChat?.otherUser?.id === 'zentro_bot' ||
                         selectedChat?.otherUser?.isBot ||
                         selectedChat?.id === 'zentro_bot_chat' ||
                         selectedChat?.name === 'Zentro Bot';

  return (
    <div
      className="flex h-screen"
      style={{ backgroundColor: currentTheme.colors.background }}
    >
      {/* Sidebar - Hidden on mobile when chat is selected, always visible on desktop */}
      <div className={`${selectedChat ? 'hidden lg:flex' : 'flex'} flex-shrink-0`}>
        <RealTimeDMSidebar
          onSelectChat={handleSelectChat}
          selectedChat={selectedChat}
          currentTheme={currentTheme}
          isMinimal={false}
        />
      </div>

      {/* Chat Area - Full width on mobile when chat selected */}
      <div className="flex-1 flex flex-col">
        {selectedChat ? (
          isZentroBotChat ? (
            <ZentroBotChat
              theme={currentTheme}
              onBack={handleBackToSidebar}
            />
          ) : (
            <RealTimeDMChatRoom
              chatUser={selectedChat.otherUser}
              chatId={selectedChat.id}
              onBack={handleBackToSidebar}
            />
          )
        ) : (
          <div
            className="flex-1 flex items-center justify-center"
            style={{ backgroundColor: currentTheme.colors.surface }}
          >
            <div className="text-center max-w-lg mx-auto p-6 lg:p-12">
              <div
                className="text-6xl lg:text-8xl mb-6 lg:mb-8 animate-bounce"
                style={{ color: currentTheme.colors.textMuted }}
              >
                💬
              </div>
              <h2
                className="text-2xl lg:text-3xl font-bold mb-4 lg:mb-6 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent"
                style={{ color: currentTheme.colors.text }}
              >
                Welcome to Zentro Messages
              </h2>
              <p
                className="text-lg lg:text-xl mb-6 lg:mb-8 font-medium"
                style={{ color: currentTheme.colors.textMuted }}
              >
                Select a conversation from the sidebar to start chatting with friends in real-time,
                share media, and enjoy our beautiful themes.
              </p>
              <div className="grid grid-cols-1 gap-3 lg:gap-4 text-sm lg:text-base" style={{ color: currentTheme.colors.textSecondary }}>
                <div className="flex items-center justify-center space-x-2 lg:space-x-3 p-2 lg:p-3 rounded-xl bg-blue-50 dark:bg-blue-900/20">
                  <span className="text-xl lg:text-2xl">🔄</span>
                  <span className="font-medium">Real-time messaging</span>
                </div>
                <div className="flex items-center justify-center space-x-2 lg:space-x-3 p-2 lg:p-3 rounded-xl bg-green-50 dark:bg-green-900/20">
                  <span className="text-xl lg:text-2xl">📸</span>
                  <span className="font-medium">Share photos and videos</span>
                </div>
                <div className="flex items-center justify-center space-x-2 lg:space-x-3 p-2 lg:p-3 rounded-xl bg-purple-50 dark:bg-purple-900/20">
                  <span className="text-xl lg:text-2xl">🎨</span>
                  <span className="font-medium">Multiple beautiful themes</span>
                </div>
                <div className="flex items-center justify-center space-x-2 lg:space-x-3 p-2 lg:p-3 rounded-xl bg-pink-50 dark:bg-pink-900/20">
                  <span className="text-xl lg:text-2xl">🤖</span>
                  <span className="font-medium">AI assistant (Zentro Bot)</span>
                </div>
                <div className="flex items-center justify-center space-x-2 lg:space-x-3 p-2 lg:p-3 rounded-xl bg-yellow-50 dark:bg-yellow-900/20">
                  <span className="text-xl lg:text-2xl">😊</span>
                  <span className="font-medium">Reactions and replies</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default RealTimeDMView;
