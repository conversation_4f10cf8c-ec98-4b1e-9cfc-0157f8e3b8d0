// Friends Service - Handle friend requests, friendships, and friend-related functionality
// Using localStorage for compatibility with current setup

class FriendsService {
  constructor() {
    this.STORAGE_KEYS = {
      USERS: 'zentro_registered_users',
      FRIEND_REQUESTS: 'zentro_friend_requests',
      FRIENDSHIPS: 'zentro_friendships'
    };
    this.friendRequestsCache = new Map();
    this.friendsCache = new Map();
  }

  // Get all registered users (simulated database)
  getRegisteredUsers() {
    const users = localStorage.getItem(this.STORAGE_KEYS.USERS);
    return users ? JSON.parse(users) : [];
  }

  // Register a new user (called when user signs up/logs in)
  registerUser(userProfile) {
    const users = this.getRegisteredUsers();
    const existingUserIndex = users.findIndex(u => u.uid === userProfile.uid);

    const userData = {
      uid: userProfile.uid,
      displayName: userProfile.displayName,
      email: userProfile.email,
      photoURL: userProfile.photoURL,
      location: userProfile.location || '',
      joinedAt: existingUserIndex === -1 ? new Date().toISOString() : users[existingUserIndex].joinedAt,
      lastActive: new Date().toISOString()
    };

    if (existingUserIndex === -1) {
      users.push(userData);
    } else {
      users[existingUserIndex] = userData;
    }

    localStorage.setItem(this.STORAGE_KEYS.USERS, JSON.stringify(users));
    return userData;
  }

  // Search for users by name, email, or username
  async searchUsers(query) {
    const users = this.getRegisteredUsers();
    const searchTerm = query.toLowerCase().trim();

    return users.filter(user =>
      user.displayName?.toLowerCase().includes(searchTerm) ||
      user.email?.toLowerCase().includes(searchTerm)
    );
  }

  // Get friend requests
  getFriendRequests() {
    const requests = localStorage.getItem(this.STORAGE_KEYS.FRIEND_REQUESTS);
    return requests ? JSON.parse(requests) : [];
  }

  // Get friendships
  getFriendships() {
    const friendships = localStorage.getItem(this.STORAGE_KEYS.FRIENDSHIPS);
    return friendships ? JSON.parse(friendships) : [];
  }

  // Send friend request
  async sendFriendRequest(fromUserId, toUserId, fromUserData) {
    const requests = this.getFriendRequests();

    // Check if request already exists
    const existingRequest = requests.find(req =>
      (req.fromUserId === fromUserId && req.toUserId === toUserId) ||
      (req.fromUserId === toUserId && req.toUserId === fromUserId)
    );

    if (existingRequest) {
      throw new Error('Friend request already exists');
    }

    // Check if already friends
    const friendships = this.getFriendships();
    const existingFriendship = friendships.find(friendship =>
      (friendship.user1Id === fromUserId && friendship.user2Id === toUserId) ||
      (friendship.user1Id === toUserId && friendship.user2Id === fromUserId)
    );

    if (existingFriendship) {
      throw new Error('You are already friends with this user');
    }

    // Create new friend request
    const newRequest = {
      id: `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      fromUserId,
      toUserId,
      fromUserData: {
        uid: fromUserData.uid,
        displayName: fromUserData.displayName,
        photoURL: fromUserData.photoURL,
        email: fromUserData.email
      },
      status: 'pending',
      createdAt: new Date().toISOString()
    };

    requests.push(newRequest);
    localStorage.setItem(this.STORAGE_KEYS.FRIEND_REQUESTS, JSON.stringify(requests));

    return newRequest;
  }

  // Accept friend request
  async acceptFriendRequest(requestId, currentUserId) {
    const requests = this.getFriendRequests();
    const requestIndex = requests.findIndex(req => req.id === requestId);

    if (requestIndex === -1) {
      throw new Error('Friend request not found');
    }

    const request = requests[requestIndex];

    if (request.toUserId !== currentUserId) {
      throw new Error('You can only accept requests sent to you');
    }

    // Create friendship
    const friendships = this.getFriendships();
    const newFriendship = {
      id: `friendship_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      user1Id: request.fromUserId,
      user2Id: request.toUserId,
      createdAt: new Date().toISOString()
    };

    friendships.push(newFriendship);
    localStorage.setItem(this.STORAGE_KEYS.FRIENDSHIPS, JSON.stringify(friendships));

    // Remove the request
    requests.splice(requestIndex, 1);
    localStorage.setItem(this.STORAGE_KEYS.FRIEND_REQUESTS, JSON.stringify(requests));

    return newFriendship;
  }

  // Reject friend request
  async rejectFriendRequest(requestId, currentUserId) {
    const requests = this.getFriendRequests();
    const requestIndex = requests.findIndex(req => req.id === requestId);

    if (requestIndex === -1) {
      throw new Error('Friend request not found');
    }

    const request = requests[requestIndex];

    if (request.toUserId !== currentUserId) {
      throw new Error('You can only reject requests sent to you');
    }

    // Remove the request
    requests.splice(requestIndex, 1);
    localStorage.setItem(this.STORAGE_KEYS.FRIEND_REQUESTS, JSON.stringify(requests));

    return true;
  }

  // Get pending friend requests for a user
  getPendingRequests(userId) {
    const requests = this.getFriendRequests();
    return requests.filter(req => req.toUserId === userId && req.status === 'pending');
  }

  // Get user's friends
  getUserFriends(userId) {
    const friendships = this.getFriendships();
    const users = this.getRegisteredUsers();

    const userFriendships = friendships.filter(friendship =>
      friendship.user1Id === userId || friendship.user2Id === userId
    );

    return userFriendships.map(friendship => {
      const friendId = friendship.user1Id === userId ? friendship.user2Id : friendship.user1Id;
      const friendData = users.find(user => user.uid === friendId);
      return {
        ...friendData,
        friendshipId: friendship.id,
        friendsSince: friendship.createdAt
      };
    }).filter(friend => friend.uid); // Filter out any missing user data
  }

  // Legacy method for compatibility
  async getFriends(userId) {
    return this.getUserFriends(userId);
  }

  // Legacy method for compatibility
  async getPendingFriendRequests(userId) {
    return this.getPendingRequests(userId);
  }

  // Check friendship status between two users
  async checkFriendshipStatus(userId1, userId2) {
    // Check if they are friends
    const friendships = this.getFriendships();
    const existingFriendship = friendships.find(friendship =>
      (friendship.user1Id === userId1 && friendship.user2Id === userId2) ||
      (friendship.user1Id === userId2 && friendship.user2Id === userId1)
    );

    if (existingFriendship) {
      return 'friends';
    }

    // Check if there's a pending request
    const requests = this.getFriendRequests();
    const existingRequest = requests.find(req =>
      (req.fromUserId === userId1 && req.toUserId === userId2) ||
      (req.fromUserId === userId2 && req.toUserId === userId1)
    );

    if (existingRequest) {
      return 'pending';
    }

    return 'none';
  }

  // Remove friend
  async removeFriend(userId1, userId2) {
    const friendships = this.getFriendships();
    const friendshipIndex = friendships.findIndex(friendship =>
      (friendship.user1Id === userId1 && friendship.user2Id === userId2) ||
      (friendship.user1Id === userId2 && friendship.user2Id === userId1)
    );

    if (friendshipIndex === -1) {
      throw new Error('Friendship not found');
    }

    friendships.splice(friendshipIndex, 1);
    localStorage.setItem(this.STORAGE_KEYS.FRIENDSHIPS, JSON.stringify(friendships));

    return true;
  }

  // Cancel friend request (for sender)
  async cancelFriendRequest(requestId, currentUserId) {
    const requests = this.getFriendRequests();
    const requestIndex = requests.findIndex(req => req.id === requestId);

    if (requestIndex === -1) {
      throw new Error('Friend request not found');
    }

    const request = requests[requestIndex];

    if (request.fromUserId !== currentUserId) {
      throw new Error('You can only cancel requests you sent');
    }

    // Remove the request
    requests.splice(requestIndex, 1);
    localStorage.setItem(this.STORAGE_KEYS.FRIEND_REQUESTS, JSON.stringify(requests));

    return true;
  }

  // Get friend count for a user
  getFriendCount(userId) {
    const friends = this.getUserFriends(userId);
    return friends.length;
  }

  // Check if user exists
  async checkUserExists(userId) {
    const users = this.getRegisteredUsers();
    return users.some(user => user.uid === userId);
  }

  // Get user profile
  async getUserProfile(userId) {
    const users = this.getRegisteredUsers();
    return users.find(user => user.uid === userId) || null;
  }

  // Subscribe to notifications (localStorage-based implementation)
  subscribeNotifications(userId, callback) {
    // Since we're using localStorage, we'll simulate real-time updates
    // by checking for changes periodically
    const checkNotifications = () => {
      const requests = this.getPendingRequests(userId);
      const notifications = requests.map(request => ({
        id: request.id,
        type: 'friend_request',
        fromUserId: request.fromUserId,
        fromUserData: request.fromUserData,
        requestId: request.id,
        message: `${request.fromUserData.displayName} sent you a friend request`,
        createdAt: request.createdAt,
        read: false
      }));
      callback(notifications);
    };

    // Initial check
    checkNotifications();

    // Set up periodic checking (every 5 seconds)
    const intervalId = setInterval(checkNotifications, 5000);

    // Return unsubscribe function
    return () => {
      clearInterval(intervalId);
    };
  }

  // Mark notification as read (localStorage implementation)
  async markNotificationAsRead(notificationId) {
    // For localStorage implementation, we don't need to do anything special
    // since notifications are generated from friend requests
    // The notification will disappear when the friend request is handled
    return true;
  }

}

// Create and export singleton instance
const friendsService = new FriendsService();
export default friendsService;
