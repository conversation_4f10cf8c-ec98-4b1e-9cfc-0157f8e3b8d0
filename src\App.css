.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.neon-text {
  text-shadow: 0 0 5px #0ff, 0 0 10px #0ff, 0 0 20px #0ff;
}

.neon-border {
  border: 2px solid #00f7ff;
  box-shadow: 0 0 15px #0ff;
}

.neon-glow {
  box-shadow: 0 0 8px #00f7ff;
}
.neon-text-shadow {
  text-shadow: 0 0 5px #d946ef, 0 0 10px #d946ef;
}
.neon-glow {
  box-shadow: 0 0 10px #d946ef, 0 0 20px #d946ef;
}
.neon-text-purple {
  color: #a855f7;
  text-shadow: 0 0 10px #a855f7, 0 0 20px #a855f7, 0 0 30px #9333ea;
}

.neon-glow {
  box-shadow: 0 0 8px #a855f7, 0 0 12px #9333ea;
}
