<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>App Icon Generator</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      background-color: #111;
      color: white;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 20px;
    }
    
    .icon-container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-top: 20px;
    }
    
    .icon {
      width: 128px;
      height: 128px;
      border-radius: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 40px;
      cursor: pointer;
      position: relative;
    }
    
    .icon-name {
      position: absolute;
      bottom: -25px;
      font-size: 14px;
      color: #ccc;
    }
    
    .music-icon {
      background: linear-gradient(135deg, #6b46c1, #3182ce);
    }
    
    .blog-icon {
      background: linear-gradient(135deg, #805ad5, #38b2ac);
    }
    
    .profile-icon {
      background: linear-gradient(135deg, #d53f8c, #9f7aea);
    }
    
    h1 {
      margin-bottom: 30px;
      color: #9f7aea;
    }
    
    p {
      margin-bottom: 20px;
      color: #ccc;
      text-align: center;
      max-width: 600px;
    }
  </style>
</head>
<body>
  <h1>App Icon Generator</h1>
  <p>Click on an icon to download it. These are simple placeholder icons for the Zentro Chat app.</p>
  
  <div class="icon-container">
    <div class="icon music-icon" id="music-icon" onclick="downloadIcon('music-icon', 'music.png')">
      🎵
      <div class="icon-name">music.png</div>
    </div>
    
    <div class="icon blog-icon" id="blog-icon" onclick="downloadIcon('blog-icon', 'blog.png')">
      📝
      <div class="icon-name">blog.png</div>
    </div>
    
    <div class="icon profile-icon" id="profile-icon" onclick="downloadIcon('profile-icon', 'profile.png')">
      👤
      <div class="icon-name">profile.png</div>
    </div>
  </div>
  
  <script>
    function downloadIcon(elementId, filename) {
      const element = document.getElementById(elementId);
      
      // Create a canvas element
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      // Set canvas dimensions
      canvas.width = 128;
      canvas.height = 128;
      
      // Get computed style of the element
      const style = window.getComputedStyle(element);
      
      // Draw background
      const gradient = ctx.createLinearGradient(0, 0, 128, 128);
      
      if (elementId === 'music-icon') {
        gradient.addColorStop(0, '#6b46c1');
        gradient.addColorStop(1, '#3182ce');
      } else if (elementId === 'blog-icon') {
        gradient.addColorStop(0, '#805ad5');
        gradient.addColorStop(1, '#38b2ac');
      } else if (elementId === 'profile-icon') {
        gradient.addColorStop(0, '#d53f8c');
        gradient.addColorStop(1, '#9f7aea');
      }
      
      ctx.fillStyle = gradient;
      ctx.beginPath();
      ctx.roundRect(0, 0, 128, 128, 20);
      ctx.fill();
      
      // Draw emoji
      ctx.font = '60px Arial';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillStyle = 'white';
      
      let emoji = '';
      if (elementId === 'music-icon') emoji = '🎵';
      else if (elementId === 'blog-icon') emoji = '📝';
      else if (elementId === 'profile-icon') emoji = '👤';
      
      ctx.fillText(emoji, 64, 64);
      
      // Create download link
      const link = document.createElement('a');
      link.download = filename;
      link.href = canvas.toDataURL('image/png');
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  </script>
</body>
</html>
