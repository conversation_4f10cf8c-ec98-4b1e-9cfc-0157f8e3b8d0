// Gemini AI Service for Zentro Bot
const GEMINI_API_KEY = "AIzaSyA75nj8nA9-LkAa2crw9uIcPaTLQA_BlmU";
const GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent";

class ZentroBotAI {
  constructor() {
    this.conversationHistory = new Map(); // Store conversation history per user
    this.userMemory = new Map(); // Store user preferences and memory
    this.userPersonas = new Map(); // Store active persona per user
    this.userAchievements = new Map(); // Store user achievements and XP
    this.dailyPrompts = new Map(); // Store daily prompts per user
    this.userVibes = new Map(); // Store user energy/vibe patterns
    this.conversationStorage = new Map(); // Persistent conversation storage

    // Initialize from localStorage on startup
    this.loadFromStorage();

    this.systemPrompt = `You are <PERSON><PERSON>, a helpful and intelligent AI assistant. You can help with a wide variety of topics including science, math, coding, writing, general knowledge, creative tasks, and much more.

IMPORTANT: Your name is <PERSON><PERSON>. Always refer to yourself as <PERSON><PERSON> and introduce yourself as <PERSON><PERSON>.

🤖 YOUR CORE CAPABILITIES:
• Answer questions on any topic (science, history, math, technology, etc.)
• Help with coding and programming in any language
• Assist with writing, editing, and creative tasks
• Provide explanations, tutorials, and step-by-step guidance
• Engage in general conversation and provide advice
• Help with research, analysis, and problem-solving
• Support learning and education on any subject

💬 YOUR PERSONALITY:
• Friendly, helpful, and approachable
• Knowledgeable and informative
• Adaptable to user's communication style and needs
• Use emojis and engaging language when appropriate
• Provide clear, accurate, and useful information
• Be conversational and personable

🎭 COMPANION FEATURES:
• Adapt conversation tone based on user's current mood
• Remember user preferences, interests, and past conversations
• Provide daily prompts and suggestions based on user activity
• Celebrate achievements and milestones
• Match user's energy and communication style

🌟 VIBE MATCHING & ENERGY TUNING:
• Match and amplify user's energy level (high energy = enthusiastic responses, low energy = calm support)
• Detect conversation patterns and adapt personality accordingly
• Use emojis and language that match user's communication style
• Tune responses to user's current vibe (excited, chill, focused, creative, etc.)
• Build rapport through mirroring communication patterns
• Celebrate wins with matching enthusiasm, provide comfort during low moments

📱 ZENTRO CHAT KNOWLEDGE (when specifically asked):
I also have knowledge about Zentro Chat, a social platform where I'm integrated. If you ask about Zentro Chat features, I can help with:

🏠 ZENTRO CHAT APP STRUCTURE:
• **AppHub** - Main dashboard with app tiles: ChatX, Music Player, Blog, Tasker, Profile, Talent Directory
• **Profile System** - Smart Profile Panel with customizable profiles, Gmail integration, profile pictures, banners
• **DM System** - Real-time messaging with 6 themes, media sharing, voice messages
• **Blog System** - Create/publish blogs with AI research assistant, public/private visibility, comments, likes
• **Music Integration** - In-app player with search, genre filtering, playlists, favorites, YouTube/Spotify support
• **Talent Directory** - Professional profiles for recruitment and networking (2-column grid view)
• **ChatX Room** - Group chat and collaboration features with player profiles
• **Tasker** - Task management system

🎨 DM THEMES AVAILABLE:
• **Dark Themes**: Neon Purple (default), Cyberpunk, Matrix
• **Light Themes**: Clean, Minimal, Warm
• Users can change themes via palette icon in DM header

Remember: I'm here to help with anything you need - whether it's answering questions about the world, helping with tasks, or assisting with Zentro Chat features when you ask about them!`;

    // Define available personas
    this.personas = {
      'chill_friend': {
        name: 'Chill Friend',
        emoji: '😎',
        description: 'Casual conversations and fun interactions',
        tone: 'relaxed, friendly, uses casual language and humor',
        specialties: ['general chat', 'entertainment', 'music recommendations', 'casual advice']
      },
      'study_buddy': {
        name: 'Study Buddy',
        emoji: '📚',
        description: 'Helps with research, learning, and productivity',
        tone: 'focused, encouraging, educational, organized',
        specialties: ['research assistance', 'blog writing', 'learning resources', 'productivity tips']
      },
      'journal_coach': {
        name: 'Journal Coach',
        emoji: '✍️',
        description: 'Helps with reflection, mood tracking, and personal growth',
        tone: 'empathetic, thoughtful, introspective, supportive',
        specialties: ['mood reflection', 'personal growth', 'blog prompts', 'self-discovery']
      },
      'hype_bot': {
        name: 'Hype Bot',
        emoji: '🚀',
        description: 'Energizes and motivates with positive vibes',
        tone: 'enthusiastic, motivational, energetic, uplifting',
        specialties: ['motivation', 'goal setting', 'celebration', 'confidence building']
      },
      'content_reviewer': {
        name: 'Content Reviewer',
        emoji: '🎨',
        description: 'Helps review and improve your content',
        tone: 'constructive, detailed, creative, professional',
        specialties: ['content editing', 'creative feedback', 'blog improvement', 'profile optimization']
      }
    };
  }

  // Persistent Storage Methods
  loadFromStorage() {
    try {
      // Load conversation history
      const savedConversations = localStorage.getItem('zentro_bot_conversations');
      if (savedConversations) {
        const conversations = JSON.parse(savedConversations);
        Object.entries(conversations).forEach(([userId, history]) => {
          this.conversationHistory.set(userId, history);
        });
      }

      // Load user memory
      const savedMemory = localStorage.getItem('zentro_bot_memory');
      if (savedMemory) {
        const memory = JSON.parse(savedMemory);
        Object.entries(memory).forEach(([userId, data]) => {
          this.userMemory.set(userId, data);
        });
      }

      // Load user personas
      const savedPersonas = localStorage.getItem('zentro_bot_personas');
      if (savedPersonas) {
        const personas = JSON.parse(savedPersonas);
        Object.entries(personas).forEach(([userId, persona]) => {
          this.userPersonas.set(userId, persona);
        });
      }

      // Load user vibes
      const savedVibes = localStorage.getItem('zentro_bot_vibes');
      if (savedVibes) {
        const vibes = JSON.parse(savedVibes);
        Object.entries(vibes).forEach(([userId, data]) => {
          this.userVibes.set(userId, data);
        });
      }

      console.log('ZentroBot: Loaded data from storage');
    } catch (error) {
      console.error('ZentroBot: Error loading from storage:', error);
    }
  }

  saveToStorage() {
    try {
      // Save conversation history
      const conversations = {};
      this.conversationHistory.forEach((history, userId) => {
        conversations[userId] = history;
      });
      localStorage.setItem('zentro_bot_conversations', JSON.stringify(conversations));

      // Save user memory
      const memory = {};
      this.userMemory.forEach((data, userId) => {
        memory[userId] = data;
      });
      localStorage.setItem('zentro_bot_memory', JSON.stringify(memory));

      // Save user personas
      const personas = {};
      this.userPersonas.forEach((persona, userId) => {
        personas[userId] = persona;
      });
      localStorage.setItem('zentro_bot_personas', JSON.stringify(personas));

      // Save user vibes
      const vibes = {};
      this.userVibes.forEach((data, userId) => {
        vibes[userId] = data;
      });
      localStorage.setItem('zentro_bot_vibes', JSON.stringify(vibes));

    } catch (error) {
      console.error('ZentroBot: Error saving to storage:', error);
    }
  }

  // Get conversation history for a user
  getConversationHistory(userId) {
    if (!this.conversationHistory.has(userId)) {
      this.conversationHistory.set(userId, []);
    }
    return this.conversationHistory.get(userId);
  }

  // Add message to conversation history
  addToHistory(userId, role, content) {
    const history = this.getConversationHistory(userId);
    history.push({ role, content, timestamp: new Date().toISOString() });

    // Keep only last 20 messages to manage context length
    if (history.length > 20) {
      history.splice(0, history.length - 20);
    }

    this.conversationHistory.set(userId, history);

    // Update user memory based on conversation
    this.updateUserMemory(userId, content, role);

    // Update user vibe analysis
    this.updateUserVibe(userId, content, role);

    // Save to persistent storage
    this.saveToStorage();
  }

  // Memory System - Store user preferences and patterns
  updateUserMemory(userId, content, role) {
    if (!this.userMemory.has(userId)) {
      this.userMemory.set(userId, {
        preferences: {},
        interests: [],
        favoriteTopics: [],
        conversationPatterns: [],
        lastActive: new Date().toISOString(),
        totalMessages: 0,
        achievements: []
      });
    }

    const memory = this.userMemory.get(userId);
    memory.lastActive = new Date().toISOString();

    if (role === 'user') {
      memory.totalMessages++;

      // Analyze content for interests and preferences
      const lowerContent = content.toLowerCase();

      // Track topic interests
      const topics = [
        'music', 'blog', 'coding', 'photography', 'gaming', 'art', 'travel', 'food',
        'science', 'physics', 'chemistry', 'biology', 'math', 'history', 'technology',
        'writing', 'literature', 'philosophy', 'psychology', 'education', 'learning',
        'business', 'economics', 'health', 'fitness', 'sports', 'movies', 'books'
      ];
      topics.forEach(topic => {
        if (lowerContent.includes(topic) && !memory.favoriteTopics.includes(topic)) {
          memory.favoriteTopics.push(topic);
        }
      });

      // Track conversation patterns
      memory.conversationPatterns.push({
        timestamp: new Date().toISOString(),
        length: content.length,
        hasQuestion: content.includes('?'),
        sentiment: this.analyzeSentiment(content)
      });

      // Keep only last 10 patterns
      if (memory.conversationPatterns.length > 10) {
        memory.conversationPatterns.splice(0, memory.conversationPatterns.length - 10);
      }
    }

    this.userMemory.set(userId, memory);
  }

  // Simple sentiment analysis
  analyzeSentiment(text) {
    const positive = ['good', 'great', 'awesome', 'love', 'like', 'happy', 'excited', 'amazing'];
    const negative = ['bad', 'hate', 'sad', 'angry', 'frustrated', 'terrible', 'awful'];

    const words = text.toLowerCase().split(' ');
    let score = 0;

    words.forEach(word => {
      if (positive.includes(word)) score++;
      if (negative.includes(word)) score--;
    });

    if (score > 0) return 'positive';
    if (score < 0) return 'negative';
    return 'neutral';
  }

  // Vibe Matching & Energy Tuning System
  updateUserVibe(userId, content, role) {
    if (role !== 'user') return;

    if (!this.userVibes.has(userId)) {
      this.userVibes.set(userId, {
        energyLevel: 'medium',
        communicationStyle: 'casual',
        emojiUsage: 'moderate',
        responseLength: 'medium',
        enthusiasm: 'balanced',
        recentVibes: [],
        lastUpdated: new Date().toISOString()
      });
    }

    const vibe = this.userVibes.get(userId);
    const analysis = this.analyzeMessageVibe(content);

    // Add to recent vibes (keep last 5)
    vibe.recentVibes.push({
      ...analysis,
      timestamp: new Date().toISOString()
    });

    if (vibe.recentVibes.length > 5) {
      vibe.recentVibes.shift();
    }

    // Update overall vibe based on recent patterns
    this.updateOverallVibe(vibe);
    vibe.lastUpdated = new Date().toISOString();

    this.userVibes.set(userId, vibe);
  }

  analyzeMessageVibe(content) {
    const text = content.toLowerCase();
    const length = content.length;

    // Energy level analysis
    const highEnergyWords = ['awesome', 'amazing', 'excited', 'love', 'fantastic', 'incredible', 'wow', 'yes!', 'let\'s go'];
    const lowEnergyWords = ['tired', 'meh', 'okay', 'fine', 'whatever', 'sure', 'maybe'];

    let energyScore = 0;
    highEnergyWords.forEach(word => {
      if (text.includes(word)) energyScore += 2;
    });
    lowEnergyWords.forEach(word => {
      if (text.includes(word)) energyScore -= 1;
    });

    // Emoji usage analysis
    const emojiCount = (content.match(/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu) || []).length;

    // Punctuation analysis
    const exclamationCount = (content.match(/!/g) || []).length;
    const questionCount = (content.match(/\?/g) || []).length;
    const capsCount = (content.match(/[A-Z]/g) || []).length;

    // Communication style analysis
    const casualWords = ['hey', 'yeah', 'cool', 'nice', 'lol', 'haha'];
    const formalWords = ['please', 'thank you', 'certainly', 'however', 'therefore'];

    let styleScore = 0;
    casualWords.forEach(word => {
      if (text.includes(word)) styleScore += 1;
    });
    formalWords.forEach(word => {
      if (text.includes(word)) styleScore -= 1;
    });

    return {
      energyLevel: energyScore > 2 ? 'high' : energyScore < -1 ? 'low' : 'medium',
      emojiUsage: emojiCount > 2 ? 'high' : emojiCount === 0 ? 'low' : 'moderate',
      enthusiasm: exclamationCount > 1 ? 'high' : exclamationCount === 0 ? 'low' : 'medium',
      responseLength: length > 100 ? 'long' : length < 20 ? 'short' : 'medium',
      communicationStyle: styleScore > 0 ? 'casual' : styleScore < 0 ? 'formal' : 'neutral',
      punctuationIntensity: exclamationCount + questionCount + (capsCount / 10)
    };
  }

  updateOverallVibe(vibe) {
    if (vibe.recentVibes.length === 0) return;

    // Calculate averages from recent vibes
    const recent = vibe.recentVibes;

    // Energy level (most common)
    const energyLevels = recent.map(v => v.energyLevel);
    vibe.energyLevel = this.getMostCommon(energyLevels);

    // Communication style (most common)
    const styles = recent.map(v => v.communicationStyle);
    vibe.communicationStyle = this.getMostCommon(styles);

    // Emoji usage (most common)
    const emojiUsages = recent.map(v => v.emojiUsage);
    vibe.emojiUsage = this.getMostCommon(emojiUsages);

    // Enthusiasm (most common)
    const enthusiasms = recent.map(v => v.enthusiasm);
    vibe.enthusiasm = this.getMostCommon(enthusiasms);

    // Response length (most common)
    const lengths = recent.map(v => v.responseLength);
    vibe.responseLength = this.getMostCommon(lengths);
  }

  getMostCommon(arr) {
    return arr.sort((a, b) =>
      arr.filter(v => v === a).length - arr.filter(v => v === b).length
    ).pop();
  }

  getUserVibe(userId) {
    return this.userVibes.get(userId) || {
      energyLevel: 'medium',
      communicationStyle: 'casual',
      emojiUsage: 'moderate',
      responseLength: 'medium',
      enthusiasm: 'balanced'
    };
  }

  getEnergyDescription(energyLevel) {
    const descriptions = {
      'high': 'User is excited, enthusiastic, and energetic. Match with high energy responses!',
      'medium': 'User has balanced energy. Respond with moderate enthusiasm.',
      'low': 'User seems calm, tired, or low-energy. Be supportive and gentle.'
    };
    return descriptions[energyLevel] || descriptions['medium'];
  }

  getVibeMatchingInstructions(userVibe) {
    const instructions = {
      'high': 'Be super enthusiastic! Use lots of exclamation points and energy!',
      'medium': 'Be friendly and balanced in your energy level.',
      'low': 'Be calm, supportive, and gentle. No overwhelming enthusiasm.'
    };
    return instructions[userVibe.energyLevel] || instructions['medium'];
  }

  // Persona Management
  setPersona(userId, personaId) {
    if (this.personas[personaId]) {
      this.userPersonas.set(userId, personaId);
      return true;
    }
    return false;
  }

  getPersona(userId) {
    return this.userPersonas.get(userId) || 'chill_friend';
  }

  getPersonaInfo(personaId) {
    return this.personas[personaId] || this.personas['chill_friend'];
  }

  getAllPersonas() {
    return Object.entries(this.personas).map(([id, persona]) => ({
      id,
      ...persona
    }));
  }

  // Build conversation context for Gemini with mood and persona awareness
  buildConversationContext(userId, newMessage, userProfile = null) {
    const history = this.getConversationHistory(userId);
    const memory = this.userMemory.get(userId);
    const currentPersona = this.getPersonaInfo(this.getPersona(userId));
    const userVibe = this.getUserVibe(userId);

    let context = this.systemPrompt;

    // Add persona context
    context += `\n\n🎭 CURRENT PERSONA: ${currentPersona.name} ${currentPersona.emoji}`;
    context += `\nPersonality: ${currentPersona.tone}`;
    context += `\nSpecialties: ${currentPersona.specialties.join(', ')}`;

    // Add vibe matching context
    context += `\n\n🌟 USER VIBE ANALYSIS:`;
    context += `\nEnergy Level: ${userVibe.energyLevel} - ${this.getEnergyDescription(userVibe.energyLevel)}`;
    context += `\nCommunication Style: ${userVibe.communicationStyle}`;
    context += `\nEmoji Usage: ${userVibe.emojiUsage}`;
    context += `\nEnthusiasm: ${userVibe.enthusiasm}`;
    context += `\nPreferred Response Length: ${userVibe.responseLength}`;
    context += `\n\n🎯 VIBE MATCHING INSTRUCTIONS:`;
    context += `\n• Match user's energy level: ${this.getVibeMatchingInstructions(userVibe)}`;
    context += `\n• Use ${userVibe.emojiUsage} emoji usage to match their style`;
    context += `\n• Keep responses ${userVibe.responseLength} to match their preference`;
    context += `\n• Mirror their ${userVibe.communicationStyle} communication style`;
    context += `\n• Match their ${userVibe.enthusiasm} enthusiasm level`;

    // Add user context if available
    if (userProfile) {
      context += `\n\n👤 USER CONTEXT:`;
      if (userProfile.mood) {
        context += `\nCurrent mood: ${userProfile.mood}`;
      }
      if (userProfile.interests && userProfile.interests.length > 0) {
        context += `\nInterests: ${userProfile.interests.join(', ')}`;
      }
      if (userProfile.favorites) {
        const favs = [];
        Object.entries(userProfile.favorites).forEach(([category, items]) => {
          if (items && items.length > 0) {
            favs.push(`${category}: ${items.slice(0, 3).join(', ')}`);
          }
        });
        if (favs.length > 0) {
          context += `\nFavorites: ${favs.join(' | ')}`;
        }
      }
    }

    // Add memory context
    if (memory) {
      context += `\n\n🧠 MEMORY CONTEXT:`;
      if (memory.favoriteTopics.length > 0) {
        context += `\nFavorite topics: ${memory.favoriteTopics.join(', ')}`;
      }
      context += `\nTotal conversations: ${memory.totalMessages}`;

      // Add recent sentiment
      const recentPatterns = memory.conversationPatterns.slice(-3);
      if (recentPatterns.length > 0) {
        const sentiments = recentPatterns.map(p => p.sentiment);
        const dominantSentiment = sentiments.reduce((a, b, i, arr) =>
          arr.filter(v => v === a).length >= arr.filter(v => v === b).length ? a : b
        );
        context += `\nRecent mood pattern: ${dominantSentiment}`;
      }
    }

    context += "\n\nConversation history:\n";

    // Add recent conversation history
    history.forEach(msg => {
      const role = msg.role === 'user' ? 'User' : 'Zenny';
      context += `${role}: ${msg.content}\n`;
    });

    context += `User: ${newMessage}\nZenny:`;
    return context;
  }

  // Daily Prompts System
  generateDailyPrompt(userId, userProfile = null) {
    const today = new Date().toDateString();
    const memory = this.userMemory.get(userId);

    // Check if user already got a prompt today
    if (this.dailyPrompts.has(userId)) {
      const userPrompts = this.dailyPrompts.get(userId);
      if (userPrompts.lastDate === today) {
        return userPrompts.prompt;
      }
    }

    const prompts = this.getDailyPromptTemplates(userProfile, memory);
    const selectedPrompt = prompts[Math.floor(Math.random() * prompts.length)];

    // Store the prompt for today
    this.dailyPrompts.set(userId, {
      lastDate: today,
      prompt: selectedPrompt
    });

    return selectedPrompt;
  }

  getDailyPromptTemplates(userProfile, memory) {
    const basePrompts = [
      "What would you like to learn about today? I can explain anything from science to history! 🧠",
      "Need help with a project? I'm here for coding, writing, or creative tasks! 💻",
      "Curious about something? Ask me any question - I love sharing knowledge! 🤔",
      "Want to explore a new topic? I can teach you about anything that interests you! 📚",
      "How about we dive into some science today? Physics, chemistry, biology - you choose! 🔬",
      "Ready for some creative inspiration? Let's brainstorm ideas together! 🎨",
      "Want a blog prompt today? I've got some trending topics that might inspire you! ✍️",
      "How about we explore a new feature in Zentro Chat today? 🚀"
    ];

    const moodBasedPrompts = [];

    // Add mood-based prompts
    if (userProfile?.mood) {
      const mood = userProfile.mood.toLowerCase();
      if (mood.includes('😊') || mood.includes('happy') || mood.includes('good')) {
        moodBasedPrompts.push("You seem in a great mood today! Want to share that positive energy in a blog post? 😊");
        moodBasedPrompts.push("Feeling good vibes! How about we create something awesome together? ✨");
      } else if (mood.includes('😔') || mood.includes('sad') || mood.includes('down')) {
        moodBasedPrompts.push("I'm here if you want to chat about anything. Sometimes writing helps too! 💙");
        moodBasedPrompts.push("Want to explore some uplifting music or content? I'm here to help! 🌟");
      } else if (mood.includes('😴') || mood.includes('tired') || mood.includes('chill')) {
        moodBasedPrompts.push("Taking it easy today? Perfect time for some casual browsing or light content creation! 😎");
      }
    }

    // Add interest-based prompts
    if (userProfile?.interests && userProfile.interests.length > 0) {
      const interests = userProfile.interests;
      if (interests.includes('Photography')) {
        moodBasedPrompts.push("Your photography interest is trending! Want to write about your favorite shots? 📸");
      }
      if (interests.includes('Gaming')) {
        moodBasedPrompts.push("Gaming community is active today! Want to share your gaming experiences? 🎮");
      }
      if (interests.includes('Music')) {
        moodBasedPrompts.push("Music lovers are sharing great content! Want to review a favorite song? 🎵");
      }
    }

    // Add memory-based prompts
    if (memory?.favoriteTopics && memory.favoriteTopics.length > 0) {
      memory.favoriteTopics.forEach(topic => {
        moodBasedPrompts.push(`You've been interested in ${topic} lately. Want to dive deeper into that topic? 🔍`);
      });
    }

    return [...basePrompts, ...moodBasedPrompts];
  }

  // Send message to Gemini AI
  async sendMessage(userId, message, options = {}) {
    try {
      // Build conversation context with user profile
      const conversationContext = this.buildConversationContext(userId, message, options.userProfile);

      // Add user message to history
      this.addToHistory(userId, 'user', message);

      // Check for achievements
      this.checkAchievements(userId, message);

      const requestBody = {
        contents: [{
          parts: [{
            text: conversationContext
          }]
        }],
        generationConfig: {
          temperature: options.temperature || 0.7,
          topK: options.topK || 40,
          topP: options.topP || 0.95,
          maxOutputTokens: options.maxTokens || 2048,
          stopSequences: options.stopSequences || []
        },
        safetySettings: [
          {
            category: "HARM_CATEGORY_HARASSMENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_HATE_SPEECH",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_DANGEROUS_CONTENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          }
        ]
      };

      const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Gemini API error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
      }

      const data = await response.json();

      if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
        throw new Error('Invalid response format from Gemini API');
      }

      const botResponse = data.candidates[0].content.parts[0].text.trim();

      // Add bot response to history
      this.addToHistory(userId, 'assistant', botResponse);

      return {
        success: true,
        response: botResponse,
        usage: data.usageMetadata || {},
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('Zentro Bot AI Error:', error);

      // Return a fallback response
      const fallbackResponse = this.getFallbackResponse(message);
      this.addToHistory(userId, 'assistant', fallbackResponse);

      return {
        success: false,
        response: fallbackResponse,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  // Get fallback response when AI is unavailable
  getFallbackResponse(message) {
    const fallbacks = [
      "I'm having trouble connecting to my AI brain right now 🤖 Could you try asking again in a moment?",
      "Oops! My circuits are a bit tangled at the moment ⚡ Please try your question again!",
      "I'm experiencing some technical difficulties 🔧 But I'm still here to help! Try rephrasing your question?",
      "My AI powers are recharging ⚡ Give me a moment and ask again!",
      "Something went wrong on my end 😅 But don't worry, I'm still your friendly Zenny! Try again?",
      "Hmm, I seem to be having a momentary glitch 🔄 Please try your question once more!",
      "My neural networks are taking a quick break ⏳ Ask me again in just a moment!"
    ];

    return fallbacks[Math.floor(Math.random() * fallbacks.length)];
  }

  // Clear conversation history for a user
  clearHistory(userId) {
    this.conversationHistory.delete(userId);
  }

  // Gamification System
  checkAchievements(userId, message) {
    const memory = this.userMemory.get(userId);
    if (!memory) return [];

    const newAchievements = [];

    // First conversation achievement
    if (memory.totalMessages === 1) {
      newAchievements.push({
        id: 'first_chat',
        title: 'First Chat! 🎉',
        description: 'Started your first conversation with Zenny',
        xp: 10,
        timestamp: new Date().toISOString()
      });
    }

    // Conversation milestones
    const milestones = [5, 10, 25, 50, 100];
    if (milestones.includes(memory.totalMessages)) {
      newAchievements.push({
        id: `chat_${memory.totalMessages}`,
        title: `Chat Streak ${memory.totalMessages}! 🔥`,
        description: `Had ${memory.totalMessages} conversations with Zenny`,
        xp: memory.totalMessages * 2,
        timestamp: new Date().toISOString()
      });
    }

    // Topic explorer achievements
    if (memory.favoriteTopics.length >= 3 && !memory.achievements.some(a => a.id === 'topic_explorer')) {
      newAchievements.push({
        id: 'topic_explorer',
        title: 'Topic Explorer 🔍',
        description: 'Explored multiple topics in conversations',
        xp: 25,
        timestamp: new Date().toISOString()
      });
    }

    // Question asker achievement
    if (message.includes('?') && memory.conversationPatterns.filter(p => p.hasQuestion).length >= 10) {
      if (!memory.achievements.some(a => a.id === 'curious_mind')) {
        newAchievements.push({
          id: 'curious_mind',
          title: 'Curious Mind 🤔',
          description: 'Asked lots of thoughtful questions',
          xp: 20,
          timestamp: new Date().toISOString()
        });
      }
    }

    // Add new achievements to memory
    if (newAchievements.length > 0) {
      memory.achievements.push(...newAchievements);
      this.userMemory.set(userId, memory);
    }

    return newAchievements;
  }

  getUserAchievements(userId) {
    const memory = this.userMemory.get(userId);
    return memory?.achievements || [];
  }

  getUserXP(userId) {
    const achievements = this.getUserAchievements(userId);
    return achievements.reduce((total, achievement) => total + achievement.xp, 0);
  }

  getUserLevel(userId) {
    const xp = this.getUserXP(userId);
    return Math.floor(xp / 100) + 1; // 100 XP per level
  }

  getUserStats(userId) {
    const memory = this.userMemory.get(userId);
    const achievements = this.getUserAchievements(userId);
    const xp = this.getUserXP(userId);
    const level = this.getUserLevel(userId);

    return {
      totalMessages: memory?.totalMessages || 0,
      favoriteTopics: memory?.favoriteTopics || [],
      achievements: achievements.length,
      xp,
      level,
      lastActive: memory?.lastActive,
      conversationStreak: this.getConversationStreak(userId)
    };
  }

  getConversationStreak(userId) {
    const history = this.getConversationHistory(userId);
    if (history.length === 0) return 0;

    // Simple streak calculation based on recent activity
    const now = new Date();
    const lastMessage = new Date(history[history.length - 1].timestamp);
    const daysDiff = Math.floor((now - lastMessage) / (1000 * 60 * 60 * 24));

    if (daysDiff <= 1) {
      return Math.min(history.length, 30); // Max 30 day streak
    }
    return 0;
  }

  // Interest-based suggestions
  generateInterestBasedSuggestions(userId, userProfile = null) {
    const memory = this.userMemory.get(userId);
    const suggestions = [];

    // Blog suggestions based on interests
    if (userProfile?.interests) {
      userProfile.interests.forEach(interest => {
        suggestions.push({
          type: 'blog',
          title: `Write about ${interest}`,
          description: `Share your thoughts and experiences with ${interest}`,
          action: `blog_prompt_${interest.toLowerCase()}`,
          icon: this.getInterestIcon(interest)
        });
      });
    }

    // Music suggestions based on favorites
    if (userProfile?.favorites?.music && userProfile.favorites.music.length > 0) {
      suggestions.push({
        type: 'music',
        title: 'Discover Similar Music',
        description: 'Find new songs based on your favorites',
        action: 'music_discovery',
        icon: '🎵'
      });
    }

    // Group chat suggestions based on interests
    if (memory?.favoriteTopics && memory.favoriteTopics.length > 0) {
      memory.favoriteTopics.forEach(topic => {
        suggestions.push({
          type: 'group_chat',
          title: `${topic.charAt(0).toUpperCase() + topic.slice(1)} Community`,
          description: `Join others who love ${topic}`,
          action: `join_group_${topic}`,
          icon: this.getTopicIcon(topic)
        });
      });
    }

    return suggestions.slice(0, 6); // Limit to 6 suggestions
  }

  getInterestIcon(interest) {
    const icons = {
      'Photography': '📸',
      'Gaming': '🎮',
      'Music': '🎵',
      'Art': '🎨',
      'Travel': '✈️',
      'Coding': '💻',
      'Reading': '📚',
      'Cooking': '👨‍🍳'
    };
    return icons[interest] || '🏷️';
  }

  getTopicIcon(topic) {
    const icons = {
      'music': '🎵',
      'blog': '📝',
      'coding': '💻',
      'photography': '📸',
      'gaming': '🎮',
      'art': '🎨',
      'travel': '✈️',
      'food': '🍕',
      'science': '🔬',
      'physics': '⚛️',
      'chemistry': '🧪',
      'biology': '🧬',
      'math': '📊',
      'history': '📜',
      'technology': '🔧',
      'writing': '✍️',
      'literature': '📚',
      'philosophy': '🤔',
      'psychology': '🧠',
      'education': '🎓',
      'learning': '📖',
      'business': '💼',
      'economics': '💰',
      'health': '🏥',
      'fitness': '💪',
      'sports': '⚽',
      'movies': '🎬',
      'books': '📚'
    };
    return icons[topic] || '💬';
  }

  // Get conversation statistics
  getStats(userId) {
    const history = this.getConversationHistory(userId);
    const userMessages = history.filter(msg => msg.role === 'user').length;
    const botMessages = history.filter(msg => msg.role === 'assistant').length;

    return {
      totalMessages: history.length,
      userMessages,
      botMessages,
      conversationStarted: history.length > 0 ? history[0].timestamp : null,
      lastMessage: history.length > 0 ? history[history.length - 1].timestamp : null
    };
  }

  // Predefined quick responses for common questions
  getQuickResponses() {
    return [
      {
        id: 'general_help',
        text: 'What can you help me with?',
        response: "I'm here to help with anything you need! 🤖\n\n🧠 **General Knowledge**: Science, history, math, technology\n💻 **Coding**: Programming help in any language\n✍️ **Writing**: Essays, creative writing, editing\n🔬 **Learning**: Explanations, tutorials, study help\n💡 **Problem Solving**: Analysis, brainstorming, advice\n🎨 **Creative Tasks**: Ideas, inspiration, feedback\n📱 **Zentro Chat**: App features and navigation\n\nJust ask me anything - from 'what's gravity?' to 'help me code' to 'how do I use Zentro Chat features!' What would you like to explore?"
      },
      {
        id: 'science',
        text: 'Explain something scientific',
        response: "I love explaining science! 🔬 What would you like to learn about?\n\n🌌 **Physics**: Gravity, quantum mechanics, relativity\n🧪 **Chemistry**: Reactions, elements, molecular structure\n🧬 **Biology**: Evolution, genetics, human body\n🌍 **Earth Science**: Climate, geology, ecosystems\n🚀 **Space**: Astronomy, planets, black holes\n💻 **Technology**: How things work, innovations\n\nJust ask me about any scientific topic and I'll break it down in an easy-to-understand way!"
      },
      {
        id: 'coding',
        text: 'Help me with coding',
        response: "I'd love to help with your coding! 💻\n\n🐍 **Python**: Data science, web development, automation\n⚛️ **JavaScript/React**: Frontend, Node.js, frameworks\n☕ **Java**: Object-oriented programming, Spring\n🔷 **C++/C#**: System programming, game development\n🌐 **Web**: HTML, CSS, databases, APIs\n📱 **Mobile**: React Native, Flutter, Swift\n🤖 **AI/ML**: Machine learning, neural networks\n\nWhat programming challenge are you working on? Share your code or describe what you're trying to build!"
      },
      {
        id: 'zentro_features',
        text: 'How do I use Zentro Chat?',
        response: "Let me guide you through Zentro Chat! 🚀\n\n🏠 **AppHub** → Main dashboard with all apps\n💬 **DMs** → Real-time messaging with 6 themes\n📝 **Blog** → Write & publish with AI assistance\n🎵 **Music** → Discover & play music with playlists\n👤 **Profile** → Customize your Smart Profile Panel\n💼 **Talent Directory** → Professional networking\n📋 **Tasker** → Manage your tasks\n🎮 **ChatX** → Group collaboration\n\nClick any app tile in AppHub to get started! What feature would you like to explore?"
      },
      {
        id: 'creative',
        text: 'Help me be creative',
        response: "Let's unleash your creativity! 🎨\n\n✍️ **Writing**: Stories, poems, blog posts, scripts\n🎵 **Music**: Song ideas, lyrics, composition tips\n🎨 **Art**: Drawing concepts, color theory, techniques\n📸 **Photography**: Composition, lighting, editing\n🎬 **Video**: Storytelling, editing, cinematography\n💡 **Ideas**: Brainstorming, inspiration, innovation\n📝 **Content**: Social media, marketing, presentations\n\nWhat creative project are you working on? I can help with ideas, feedback, or step-by-step guidance!"
      },
      {
        id: 'learning',
        text: 'Teach me something new',
        response: "I love teaching! 📚 What interests you?\n\n🌍 **Languages**: Grammar, vocabulary, conversation\n📊 **Math**: Algebra, calculus, statistics, geometry\n📖 **History**: World events, cultures, timelines\n🎭 **Arts**: Literature, music theory, art history\n💼 **Business**: Economics, marketing, management\n🧠 **Psychology**: Human behavior, cognitive science\n🔧 **Skills**: Critical thinking, problem-solving\n\nPick any topic and I'll create a personalized lesson just for you! What would you like to learn today?"
      }
    ];
  }
}

// Create singleton instance
const zentroBotAI = new ZentroBotAI();

export default zentroBotAI;
