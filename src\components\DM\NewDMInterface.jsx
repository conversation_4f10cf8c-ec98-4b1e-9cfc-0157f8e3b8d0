import React, { useState, useRef, useEffect } from 'react';
import { FaPaperPlane, FaSearch, FaPlus, FaEllipsisV, FaArrowLeft, FaSmile, FaImage, FaPhone, FaVideo } from 'react-icons/fa';
import { useUser } from '../../contexts/UserContext';
import { useTheme } from '../../contexts/ThemeContext';

const NewDMInterface = () => {
  const { userProfile } = useUser();
  const { currentTheme } = useTheme();
  const [selectedChat, setSelectedChat] = useState(null);
  const [message, setMessage] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const messagesEndRef = useRef(null);

  // Mock data - replace with real data
  const [chats] = useState([
    {
      id: 1,
      name: '<PERSON><PERSON>',
      avatar: null,
      lastMessage: 'AI Assistant - Ask me anything!',
      time: 'now',
      unread: 0,
      online: true,
      isBot: true
    },
    {
      id: 2,
      name: '<PERSON>',
      avatar: null,
      lastMessage: 'Haii',
      time: '20h',
      unread: 0,
      online: true
    },
    {
      id: 3,
      name: 'Johan',
      avatar: null,
      lastMessage: 'https://media2.giphy.com/...',
      time: '5/26/2025',
      unread: 0,
      online: false
    }
  ]);

  const [messages, setMessages] = useState([]);

  const handleSendMessage = () => {
    if (!message.trim() || !selectedChat) return;
    
    const newMessage = {
      id: Date.now(),
      text: message,
      sender: userProfile,
      timestamp: new Date(),
      isOwn: true
    };
    
    setMessages(prev => [...prev, newMessage]);
    setMessage('');
    
    // Auto-scroll to bottom
    setTimeout(() => messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' }), 100);
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const filteredChats = chats.filter(chat =>
    chat.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="h-screen flex bg-white dark:bg-gray-900">
      {/* Sidebar */}
      <div className={`${selectedChat ? 'hidden lg:flex' : 'flex'} w-full lg:w-80 flex-col border-r border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800`}>
        {/* Sidebar Header */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-xl font-bold text-gray-900 dark:text-white">Messages</h1>
            <div className="flex space-x-2">
              <button className="p-2 rounded-full bg-blue-500 text-white hover:bg-blue-600 transition-colors">
                <FaSearch className="w-4 h-4" />
              </button>
              <button className="p-2 rounded-full bg-purple-500 text-white hover:bg-purple-600 transition-colors">
                <FaPlus className="w-4 h-4" />
              </button>
            </div>
          </div>
          
          {/* Search Bar */}
          <div className="relative">
            <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search conversations..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-gray-100 dark:bg-gray-700 border-0 rounded-lg text-gray-900 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* Friend Suggestions */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-3">
            <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Friend Suggestions</span>
            <button className="text-xs text-blue-500 hover:text-blue-600">Refresh Suggestions</button>
          </div>
          <div className="bg-gray-800 rounded-lg p-3 flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
              <span className="text-white font-bold">S</span>
            </div>
            <div className="flex-1">
              <p className="text-white font-medium">Stranger Plays</p>
              <p className="text-gray-400 text-sm">Mutual friend: Alex</p>
            </div>
            <button className="text-purple-400 text-sm hover:text-purple-300">Add</button>
          </div>
        </div>

        {/* Chat List */}
        <div className="flex-1 overflow-y-auto">
          {filteredChats.map(chat => (
            <div
              key={chat.id}
              onClick={() => setSelectedChat(chat)}
              className={`p-4 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors ${
                selectedChat?.id === chat.id ? 'bg-blue-50 dark:bg-blue-900/20 border-r-2 border-blue-500' : ''
              }`}
            >
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
                    {chat.avatar ? (
                      <img src={chat.avatar} alt={chat.name} className="w-full h-full rounded-full object-cover" />
                    ) : (
                      <span className="text-white font-bold text-lg">
                        {chat.isBot ? '🤖' : chat.name.charAt(0).toUpperCase()}
                      </span>
                    )}
                  </div>
                  {chat.online && (
                    <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white dark:border-gray-800"></div>
                  )}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold text-gray-900 dark:text-white truncate">{chat.name}</h3>
                    <span className="text-xs text-gray-500 dark:text-gray-400">{chat.time}</span>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 truncate">{chat.lastMessage}</p>
                </div>
                
                {chat.unread > 0 && (
                  <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                    <span className="text-xs text-white font-bold">{chat.unread}</span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Chat Area */}
      <div className={`${selectedChat ? 'flex' : 'hidden lg:flex'} flex-1 flex-col`}>
        {selectedChat ? (
          <>
            {/* Chat Header */}
            <div className="p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => setSelectedChat(null)}
                    className="lg:hidden p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
                  >
                    <FaArrowLeft className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                  </button>
                  
                  <div className="relative">
                    <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
                      <span className="text-white font-bold">
                        {selectedChat.isBot ? '🤖' : selectedChat.name.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    {selectedChat.online && (
                      <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white dark:border-gray-800"></div>
                    )}
                  </div>
                  
                  <div>
                    <h2 className="font-semibold text-gray-900 dark:text-white">{selectedChat.name}</h2>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {selectedChat.online ? 'Online' : 'Offline'}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <button className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700">
                    <FaPhone className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                  </button>
                  <button className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700">
                    <FaVideo className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                  </button>
                  <button className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700">
                    <FaEllipsisV className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                  </button>
                </div>
              </div>
            </div>

            {/* Messages Area */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-900">
              {messages.length === 0 ? (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <div className="text-6xl mb-4">💬</div>
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                      Start a conversation
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      Send a message to {selectedChat.name}
                    </p>
                  </div>
                </div>
              ) : (
                <>
                  {messages.map(msg => (
                    <div key={msg.id} className={`flex ${msg.isOwn ? 'justify-end' : 'justify-start'}`}>
                      <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-2xl ${
                        msg.isOwn 
                          ? 'bg-blue-500 text-white rounded-br-sm' 
                          : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-white rounded-bl-sm'
                      }`}>
                        <p>{msg.text}</p>
                        <p className={`text-xs mt-1 ${msg.isOwn ? 'text-blue-100' : 'text-gray-500'}`}>
                          {new Date(msg.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                        </p>
                      </div>
                    </div>
                  ))}
                  <div ref={messagesEndRef} />
                </>
              )}
            </div>

            {/* Message Input */}
            <div className="p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
              <div className="flex items-end space-x-3">
                <button className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700">
                  <FaImage className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                </button>
                
                <div className="flex-1 relative">
                  <textarea
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder={`Message ${selectedChat.name}...`}
                    rows={1}
                    className="w-full px-4 py-3 pr-12 bg-gray-100 dark:bg-gray-700 border-0 rounded-2xl resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 dark:text-white placeholder-gray-500"
                    style={{ minHeight: '44px', maxHeight: '120px' }}
                  />
                  <button className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600">
                    <FaSmile className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                  </button>
                </div>
                
                <button
                  onClick={handleSendMessage}
                  disabled={!message.trim()}
                  className="p-3 bg-blue-500 text-white rounded-full hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <FaPaperPlane className="w-4 h-4" />
                </button>
              </div>
            </div>
          </>
        ) : (
          /* Welcome Screen */
          <div className="flex-1 flex items-center justify-center bg-gray-50 dark:bg-gray-900">
            <div className="text-center max-w-md mx-auto p-8">
              <div className="text-8xl mb-6">💬</div>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                Welcome to Zentro Messages
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-400 mb-8">
                Select a conversation from the sidebar to start chatting with friends in real-time.
              </p>
              <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-400">
                <div className="flex items-center space-x-2 p-3 bg-white dark:bg-gray-800 rounded-lg">
                  <span className="text-xl">🔄</span>
                  <span>Real-time messaging</span>
                </div>
                <div className="flex items-center space-x-2 p-3 bg-white dark:bg-gray-800 rounded-lg">
                  <span className="text-xl">📸</span>
                  <span>Share media</span>
                </div>
                <div className="flex items-center space-x-2 p-3 bg-white dark:bg-gray-800 rounded-lg">
                  <span className="text-xl">🎨</span>
                  <span>Beautiful themes</span>
                </div>
                <div className="flex items-center space-x-2 p-3 bg-white dark:bg-gray-800 rounded-lg">
                  <span className="text-xl">🤖</span>
                  <span>AI assistants</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default NewDMInterface;
