import React, { useState } from 'react';
import { FaSearch, FaRobot, FaTimes, FaUserPlus, FaSpinner, FaTrash } from 'react-icons/fa';
import { useRealTimeChatList } from '../../hooks/useRealTimeChatList';
import ConversationSearch from './ConversationSearch';
import FriendSuggestions from '../Friends/FriendSuggestions';

const RealTimeDMSidebar = ({ onSelectChat, selectedChat, currentTheme, isMinimal = false }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showConversationSearch, setShowConversationSearch] = useState(false);

  const { chats, loading, startNewChat, deleteChat } = useRealTimeChatList();

  const filteredChats = chats.filter(chat =>
    chat.otherUser.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
    chat.otherUser.name !== 'Unknown User' &&
    chat.otherUser.name.trim() !== ''
  );

  // Temporary function to clear localStorage and start fresh
  const clearAllData = () => {
    if (window.confirm('Clear all localStorage data and start fresh? This will remove all friends, chats, and user data.')) {
      localStorage.clear();
      window.location.reload();
    }
  };

  const formatTime = (timestamp) => {
    if (!timestamp) return '';

    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return 'now';
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h`;
    } else {
      return date.toLocaleDateString();
    }
  };



  const handleStartNewChat = async (user) => {
    try {
      const chatId = await startNewChat(user.id);
      if (chatId) {
        // The chat will appear in the list automatically due to real-time subscription
        console.log('New chat started:', chatId);
      }
    } catch (error) {
      console.error('Error starting new chat:', error);
    }
  };

  const handleDeleteChat = async (chatId, chatName, e) => {
    e.stopPropagation(); // Prevent chat selection

    if (window.confirm(`Are you sure you want to delete the conversation with ${chatName}? This action cannot be undone.`)) {
      try {
        if (deleteChat) {
          await deleteChat(chatId);
        }
        // If this was the selected chat, clear selection
        if (selectedChat?.id === chatId) {
          onSelectChat(null);
        }
      } catch (error) {
        console.error('Error deleting chat:', error);
        alert('Failed to delete chat. Please try again.');
      }
    }
  };

  if (loading) {
    return (
      <div
        className="w-80 h-full flex items-center justify-center border-r"
        style={{
          backgroundColor: currentTheme?.colors?.surface || '#1F2937',
          borderColor: currentTheme?.colors?.borderMuted || '#374151'
        }}
      >
        <div className="flex items-center space-x-2">
          <FaSpinner className="animate-spin" style={{ color: currentTheme?.colors?.primary }} />
          <span style={{ color: currentTheme?.colors?.text }}>Loading chats...</span>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`${isMinimal ? 'w-16' : 'w-80'} h-full flex flex-col border-r transition-all duration-300 backdrop-blur-sm`}
      style={{
        backgroundColor: currentTheme?.colors?.surface || '#FAFAFA',
        borderColor: currentTheme?.colors?.borderMuted || '#E5E7EB'
      }}
    >
      {/* Header */}
      {!isMinimal && (
        <div className="p-6 border-b border-gray-100 dark:border-gray-800 bg-white/50 dark:bg-gray-900/50 backdrop-blur-md">
          <div className="flex items-center justify-between mb-6">
            <h2
              className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent"
              style={{ color: currentTheme?.colors?.text || '#111827' }}
            >
              Messages
            </h2>
            <div className="flex items-center gap-2">
              <button
                onClick={() => setShowConversationSearch(true)}
                className="p-2.5 rounded-xl hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200 group"
                title="Find people & chat requests"
              >
                <FaUserPlus
                  className="text-lg group-hover:scale-110 transition-transform duration-200"
                  style={{ color: currentTheme?.colors?.primary || '#3B82F6' }}
                />
              </button>
              <button
                onClick={clearAllData}
                className="p-2.5 rounded-xl hover:bg-red-50 dark:hover:bg-red-900/20 transition-all duration-200 group"
                title="Clear All Data (Debug)"
              >
                <FaTrash
                  className="text-lg group-hover:scale-110 transition-transform duration-200"
                  style={{ color: '#EF4444' }}
                />
              </button>
            </div>
          </div>

        {/* Search */}
        <div className="relative">
          <FaSearch
            className="absolute left-4 top-1/2 transform -translate-y-1/2 text-sm transition-colors duration-200"
            style={{ color: currentTheme?.colors?.textMuted || '#9CA3AF' }}
          />
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search conversations..."
            className="w-full pl-12 pr-4 py-3 rounded-2xl border-0 bg-gray-50 dark:bg-gray-800/50 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white dark:focus:bg-gray-700 transition-all duration-200 text-sm font-medium placeholder:text-gray-400"
            style={{
              backgroundColor: currentTheme?.colors?.inputBackground || '#F9FAFB',
              color: currentTheme?.colors?.text || '#111827'
            }}
          />
        </div>


      </div>
      )}

      {/* Friend Suggestions Section - Hidden in minimal mode */}
      {!isMinimal && (
        <div className="border-b" style={{ borderColor: currentTheme?.colors?.borderMuted }}>
          <FriendSuggestions />
        </div>
      )}

      {/* Chat List */}
      <div className="flex-1 overflow-y-auto">
        {filteredChats.length === 0 ? (
          <div className={`flex items-center justify-center h-32 ${isMinimal ? 'px-2' : ''}`}>
            {!isMinimal && (
              <p style={{ color: currentTheme?.colors?.textMuted || '#9CA3AF' }}>
                No conversations found
              </p>
            )}
          </div>
        ) : (
          filteredChats.map(chat => (
            <div
              key={chat.id}
              onClick={() => onSelectChat(chat)}
              className={`group flex items-center ${isMinimal ? 'p-3 justify-center mx-2 my-1 rounded-xl' : 'p-4 mx-3 my-1 rounded-2xl'} cursor-pointer transition-all duration-200 hover:shadow-md ${
                selectedChat?.id === chat.id
                  ? 'bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30 shadow-lg border border-blue-200/50 dark:border-blue-700/50'
                  : 'hover:bg-gray-50 dark:hover:bg-gray-800/50'
              }`}
              style={{
                backgroundColor: selectedChat?.id === chat.id
                  ? currentTheme?.colors?.surfaceVariant
                  : 'transparent'
              }}
              title={isMinimal ? chat.otherUser.name : ''}
            >
              {/* Avatar */}
              <div className={`relative flex-shrink-0 ${isMinimal ? 'mr-0' : 'mr-4'}`}>
                <div className={`${isMinimal ? 'w-10 h-10' : 'w-14 h-14'} rounded-full overflow-hidden bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-800 ring-2 ring-white/50 dark:ring-gray-600/50 shadow-lg transition-all duration-200 group-hover:ring-blue-200 dark:group-hover:ring-blue-700`}>
                  {chat.otherUser.isBot ? (
                    <div className="w-full h-full flex items-center justify-center text-white font-semibold bg-gradient-to-br from-purple-500 via-purple-600 to-pink-500 shadow-inner">
                      <FaRobot className={`${isMinimal ? 'text-sm' : 'text-lg'}`} />
                    </div>
                  ) : chat.otherUser.avatar ? (
                    <img
                      src={chat.otherUser.avatar}
                      alt={chat.otherUser.name}
                      className="w-full h-full object-cover transition-all duration-200 group-hover:scale-105"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-white bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 font-semibold shadow-inner">
                      {chat.otherUser.name.charAt(0).toUpperCase()}
                    </div>
                  )}
                </div>
                {chat.otherUser.online && (
                  <div className="absolute -bottom-0.5 -right-0.5 w-4 h-4 bg-green-500 rounded-full border-3 border-white dark:border-gray-900 shadow-sm animate-pulse"></div>
                )}
              </div>

              {/* Content - Hidden in minimal mode */}
              {!isMinimal && (
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-2">
                    <h3
                      className="font-semibold truncate text-base"
                      style={{ color: currentTheme?.colors?.text || '#111827' }}
                    >
                      {chat.otherUser.isBot ? 'Zenny' : chat.otherUser.name}
                    </h3>
                    <div className="flex items-center space-x-3">
                      <span
                        className="text-xs font-medium"
                        style={{ color: currentTheme?.colors?.textMuted || '#6B7280' }}
                      >
                        {formatTime(chat.lastMessageTime)}
                      </span>
                      {chat.unreadCount > 0 && (
                        <div className="min-w-[22px] h-6 rounded-full flex items-center justify-center text-xs text-white font-bold bg-gradient-to-r from-blue-500 to-blue-600 shadow-lg animate-pulse">
                          {chat.unreadCount > 99 ? '99+' : chat.unreadCount}
                        </div>
                      )}
                    </div>
                  </div>
                  <p
                    className="text-sm truncate font-medium leading-relaxed"
                    style={{ color: currentTheme?.colors?.textMuted || '#6B7280' }}
                  >
                    {chat.lastMessage || 'Start a conversation...'}
                  </p>
                </div>
              )}

              {/* Unread indicator for minimal mode */}
              {isMinimal && chat.unreadCount > 0 && (
                <div className="absolute -top-1 -right-1 min-w-[18px] h-5 rounded-full flex items-center justify-center text-xs text-white font-bold bg-gradient-to-r from-blue-500 to-blue-600 shadow-lg animate-pulse">
                  {chat.unreadCount > 9 ? '9+' : chat.unreadCount}
                </div>
              )}
            </div>
          ))
        )}
      </div>

      {/* Conversation Search Modal */}
      {showConversationSearch && (
        <ConversationSearch
          onSelectUser={(user) => {
            handleStartNewChat(user);
            setShowConversationSearch(false);
          }}
          onClose={() => setShowConversationSearch(false)}
          currentTheme={currentTheme}
        />
      )}
    </div>
  );
};

export default RealTimeDMSidebar;
