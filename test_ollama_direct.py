import requests
import json
import sys

def test_ollama():
    print("Testing direct connection to Ollama...")
    
    # Try multiple URLs
    urls = [
        "http://127.0.0.1:11435/api/tags",
        "http://localhost:11435/api/tags"
    ]
    
    working_url = None
    models = []
    
    for url in urls:
        try:
            print(f"\nTrying to connect to {url}...")
            response = requests.get(url, timeout=10)
            print(f"Response status: {response.status_code}")
            
            if response.status_code == 200:
                print("Connection successful!")
                data = response.json()
                models = data.get("models", [])
                print(f"Available models: {json.dumps(models, indent=2)}")
                working_url = url.replace("/api/tags", "")
                break
        except Exception as e:
            print(f"Error connecting to {url}: {str(e)}")
    
    if not working_url:
        print("\nFailed to connect to Ollama on any URL.")
        return False
    
    # Try to generate text
    print(f"\nTesting text generation using {working_url}...")
    
    # Check if tinyllama is available
    tinyllama_available = any("tinyllama" in model.lower() for model in models)
    model_to_use = "tinyllama" if tinyllama_available else "llama3"
    
    print(f"Using model: {model_to_use}")
    
    try:
        gen_response = requests.post(
            f"{working_url}/api/generate",
            json={
                "model": model_to_use,
                "prompt": "Tell me about artificial intelligence in one paragraph.",
                "stream": False
            },
            timeout=30
        )
        
        print(f"Generation response status: {gen_response.status_code}")
        
        if gen_response.status_code == 200:
            gen_data = gen_response.json()
            print("Generation successful!")
            print("\nGenerated text:")
            print(gen_data.get("response", "No response text"))
            return True
        else:
            print(f"Generation failed with status {gen_response.status_code}")
            print(f"Error: {gen_response.text}")
            return False
    except Exception as e:
        print(f"Error during generation: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_ollama()
    print("\nTest completed.")
    sys.exit(0 if success else 1)
