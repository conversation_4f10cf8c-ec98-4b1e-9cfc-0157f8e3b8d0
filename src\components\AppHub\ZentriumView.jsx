import React, { useState, useEffect } from 'react';
import { FaDownload, FaExternalLinkAlt, FaSearch, FaStar, FaCode, FaPlus, FaEdit } from 'react-icons/fa';
import { useUser } from '../../contexts/UserContext';
import AppUploader from '../Professional/AppUploader';
import AppRatingSystem from './AppRatingSystem';
import InAppBrowser from './InAppBrowser';
import professionalService from '../../services/professionalService';

const ZentriumView = () => {
  const { userProfile, isAdmin } = useUser();
  const [apps, setApps] = useState([]);
  const [filteredApps, setFilteredApps] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [selectedApp, setSelectedApp] = useState(null);
  const [showAppUploader, setShowAppUploader] = useState(false);
  const [editingApp, setEditingApp] = useState(null);
  const [showInAppBrowser, setShowInAppBrowser] = useState(false);
  const [browserUrl, setBrowserUrl] = useState('');

  const categories = ['all', 'productivity', 'games', 'utilities', 'social', 'education', 'business'];

  // Handle clearing the database
  const handleClearDatabase = () => {
    if (window.confirm('Are you sure you want to clear the entire app showcase database? This action cannot be undone.')) {
      professionalService.clearAppShowcase();
      setApps([]);
      console.log('App showcase database cleared');
    }
  };

  // Load apps on component mount
  useEffect(() => {
    const loadApps = () => {
      try {
        const stored = localStorage.getItem('zentro_apps');
        const data = stored ? JSON.parse(stored) : [];
        const publicApps = data.filter(app => app.visibility === 'public');
        setApps(publicApps);
      } catch (error) {
        console.error('Error loading apps:', error);
        setApps([]);
      }
    };

    loadApps();
  }, []);

  // Filter apps based on search and category
  useEffect(() => {
    let filtered = apps;

    if (searchQuery) {
      filtered = filtered.filter(app =>
        app.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        app.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        app.description?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    if (categoryFilter !== 'all') {
      filtered = filtered.filter(app => app.category === categoryFilter);
    }

    setFilteredApps(filtered);
  }, [apps, searchQuery, categoryFilter]);

  const AppCard = ({ app }) => {
    const isOwner = app.userId === userProfile?.uid;

    const handleEdit = (e) => {
      e.stopPropagation();
      setEditingApp(app);
      setShowAppUploader(true);
    };

    return (
      <div className="bg-gray-900 border border-gray-700 rounded-xl p-6 hover:border-purple-500 transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/20 cursor-pointer relative group"
           onClick={() => setSelectedApp(app)}>

        {/* Edit Button for Owner */}
        {isOwner && (
          <button
            onClick={handleEdit}
            className="absolute top-3 right-3 p-2 bg-gray-800 hover:bg-gray-700 text-gray-400 hover:text-white rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300 z-10"
            title="Edit App"
          >
            <FaEdit className="text-sm" />
          </button>
        )}

        {/* App Icon */}
        <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-blue-500 rounded-xl flex items-center justify-center mb-4">
          <FaCode className="text-2xl text-white" />
        </div>

        {/* App Info */}
        <h3 className="text-xl font-bold text-white mb-2">{app.title || app.name || 'Untitled App'}</h3>
        <p className="text-gray-400 text-sm mb-4 line-clamp-2">{app.description || 'No description available'}</p>

        {/* Stats */}
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1 text-yellow-400">
              <FaStar className="text-xs" />
              <span>{app.rating || '4.5'}</span>
            </div>
            <div className="flex items-center space-x-1 text-green-400">
              <FaDownload className="text-xs" />
              <span>{app.downloads || 0}</span>
            </div>
          </div>
          <span className="text-purple-400 text-xs px-2 py-1 bg-purple-500/20 rounded-full">
            {app.category || 'General'}
          </span>
        </div>
      </div>
    );
  };

  const AppModal = ({ app, onClose }) => {
    const [currentApp, setCurrentApp] = useState(app);

    if (!app) return null;

    const handleDownload = () => {
      if (currentApp.downloadUrl) {
        window.open(currentApp.downloadUrl, '_blank');
        // Increment download count
        const updatedApp = { ...currentApp, downloads: (currentApp.downloads || 0) + 1 };
        professionalService.saveApp(updatedApp);
        setCurrentApp(updatedApp);
        // Update the main apps list
        setApps(prevApps => prevApps.map(a => a.id === updatedApp.id ? updatedApp : a));
      }
    };

    const handleOpenDemo = () => {
      if (currentApp.demoUrl) {
        // Open demo in in-app browser instead of external window
        setBrowserUrl(currentApp.demoUrl);
        setShowInAppBrowser(true);
      }
    };

    return (
      <div className="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-50 p-4">
        <div className="bg-gray-900 rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden border border-gray-700">
          {/* Header */}
          <div className="p-6 border-b border-gray-700">
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-4">
                <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-blue-500 rounded-xl flex items-center justify-center">
                  <FaCode className="text-3xl text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-white mb-2">{currentApp.title || currentApp.name || 'Untitled App'}</h2>
                  <p className="text-gray-400 mb-2">{currentApp.description || 'No description available'}</p>
                  <div className="flex items-center space-x-4 text-sm">
                    <div className="flex items-center space-x-1 text-yellow-400">
                      <FaStar />
                      <span>{currentApp.rating || '4.5'}</span>
                    </div>
                    <div className="flex items-center space-x-1 text-green-400">
                      <FaDownload />
                      <span>{currentApp.downloads || 0} downloads</span>
                    </div>
                    <span className="text-purple-400 px-2 py-1 bg-purple-500/20 rounded-full">
                      {currentApp.category || 'General'}
                    </span>
                  </div>
                </div>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-white text-2xl"
              >
                ×
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto max-h-[60vh]">
            {/* Screenshots/Demo */}
            {currentApp.screenshots && currentApp.screenshots.length > 0 && (
              <div className="mb-6">
                <h3 className="text-xl font-semibold text-white mb-4">Screenshots</h3>
                <div className="grid grid-cols-2 gap-4">
                  {currentApp.screenshots.map((screenshot, index) => (
                    <img
                      key={index}
                      src={screenshot}
                      alt={`Screenshot ${index + 1}`}
                      className="w-full h-48 object-cover rounded-lg border border-gray-700"
                    />
                  ))}
                </div>
              </div>
            )}

            {/* Features */}
            {currentApp.features && currentApp.features.length > 0 && (
              <div className="mb-6">
                <h3 className="text-xl font-semibold text-white mb-4">Features</h3>
                <ul className="space-y-2">
                  {currentApp.features.map((feature, index) => (
                    <li key={index} className="flex items-center space-x-2 text-gray-300">
                      <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Tech Stack */}
            {currentApp.techStack && currentApp.techStack.length > 0 && (
              <div className="mb-6">
                <h3 className="text-xl font-semibold text-white mb-4">Tech Stack</h3>
                <div className="flex flex-wrap gap-2">
                  {currentApp.techStack.map((tech, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-gray-800 text-gray-300 text-sm rounded-full border border-gray-700"
                    >
                      {tech}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Rating System */}
            <AppRatingSystem app={currentApp} />
          </div>

          {/* Footer */}
          <div className="p-6 border-t border-gray-700 bg-gray-800/50">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-400">
                <p>Created by {currentApp.creatorName || 'Anonymous'}</p>
                <p>Updated {new Date(currentApp.updatedAt || currentApp.createdAt).toLocaleDateString()}</p>
              </div>

              <div className="flex items-center space-x-3">
                {currentApp.demoUrl && (
                  <button
                    onClick={handleOpenDemo}
                    className="flex items-center space-x-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                  >
                    <FaExternalLinkAlt />
                    <span>Try Demo</span>
                  </button>
                )}

                {currentApp.downloadUrl && (
                  <button
                    onClick={handleDownload}
                    className="flex items-center space-x-2 px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                  >
                    <FaDownload />
                    <span>Download</span>
                  </button>
                )}

                {currentApp.githubUrl && (
                  <button
                    onClick={() => window.open(currentApp.githubUrl, '_blank')}
                    className="flex items-center space-x-2 px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
                  >
                    <FaCode />
                    <span>Source Code</span>
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="flex-1 overflow-y-auto p-6 bg-gray-950">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-4xl font-bold mb-2 bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
                Zentrium
              </h1>
              <p className="text-gray-400 text-lg">
                Professional Application Hub - Discover and explore amazing applications built by our community
              </p>
            </div>
            <div className="flex items-center space-x-3">
              {isAdmin() && (
                <button
                  onClick={handleClearDatabase}
                  className="flex items-center space-x-2 px-4 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-all duration-300 text-sm"
                >
                  <span>Clear Database</span>
                </button>
              )}
              <button
                onClick={() => setShowAppUploader(true)}
                className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-lg transition-all duration-300 shadow-lg hover:shadow-purple-500/25"
              >
                <FaPlus className="text-lg" />
                <span className="font-medium">Upload App</span>
              </button>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="mb-8 space-y-4">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1 relative">
              <FaSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search apps..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-12 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors"
              />
            </div>

            {/* Category Filter */}
            <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-purple-500 transition-colors"
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category === 'all' ? 'All Categories' : category.charAt(0).toUpperCase() + category.slice(1)}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Apps Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredApps.map((app) => (
            <AppCard key={app.id} app={app} />
          ))}
        </div>

        {/* Empty State */}
        {filteredApps.length === 0 && (
          <div className="text-center py-16">
            <FaCode className="text-6xl text-gray-600 mx-auto mb-4" />
            <h3 className="text-2xl font-bold text-gray-400 mb-2">No apps found</h3>
            <p className="text-gray-500">
              {searchQuery || categoryFilter !== 'all'
                ? 'Try adjusting your search filters'
                : 'Be the first to submit an app!'}
            </p>
          </div>
        )}
      </div>

      {/* App Modal */}
      {selectedApp && (
        <AppModal app={selectedApp} onClose={() => setSelectedApp(null)} />
      )}

      {/* App Uploader Modal */}
      {showAppUploader && (
        <AppUploader
          editingApp={editingApp}
          onClose={() => {
            setShowAppUploader(false);
            setEditingApp(null);
          }}
          onAppUploaded={(app) => {
            console.log('App uploaded:', app);
            setShowAppUploader(false);
            setEditingApp(null);
            // Reload apps to show the new one
            const loadApps = () => {
              try {
                const stored = localStorage.getItem('zentro_apps');
                const data = stored ? JSON.parse(stored) : [];
                const publicApps = data.filter(app => app.visibility === 'public');
                setApps(publicApps);
              } catch (error) {
                console.error('Error loading apps:', error);
                setApps([]);
              }
            };
            loadApps();
          }}
        />
      )}

      {/* In-App Browser */}
      {showInAppBrowser && (
        <InAppBrowser
          url={browserUrl}
          onClose={() => setShowInAppBrowser(false)}
        />
      )}
    </div>
  );
};

export default ZentriumView;
